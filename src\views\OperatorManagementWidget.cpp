#include "OperatorManagementWidget.h"
#include "ui_OperatorManagementWidget.h"
#include <QTableWidget>
#include <QHeaderView>
#include <QMessageBox>
#include <QDateTime>
#include <QDebug>

namespace AccessControl {

OperatorManagementWidget::OperatorManagementWidget(std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent)
    : QWidget(parent),
    ui(new Ui::OperatorManagementWidget),
    m_databaseProvider(dbProvider)
{
    ui->setupUi(this);

    // 初始化DAO
    m_operatorDao = std::make_unique<OperatorDao>(m_databaseProvider);

    // 获取UI元素引用
    m_operatorTable = ui->operatorTableWidget;
    m_usernameSearchEdit = ui->usernameSearchEdit;
    m_roleFilterCombo = ui->roleFilterCombo;
    m_totalLabel = ui->totalLabel;
    m_statusLabel = ui->statusLabel;

    // 获取按钮引用
    m_addButton = ui->addButton;
    m_editButton = ui->editButton;
    m_deleteButton = ui->deleteButton;
    m_resetPasswordButton = ui->resetPasswordButton;
    m_changeStatusButton = ui->changeStatusButton;
    m_searchButton = ui->searchButton;
    m_filterButton = ui->filterButton;
    m_clearFilterButton = ui->clearFilterButton;

    // 设置界面
    setupUi();
    setupConnections();

    // 加载数据
    loadOperators();
}

OperatorManagementWidget::~OperatorManagementWidget()
{
    delete ui;
}

void OperatorManagementWidget::setupUi()
{
    setupTableHeaders();

    // 设置表格属性
    m_operatorTable->setAlternatingRowColors(true);
    m_operatorTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_operatorTable->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_operatorTable->setSortingEnabled(true);
    m_operatorTable->horizontalHeader()->setStretchLastSection(true);

    // 设置表格列调整模式
    m_operatorTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);

    // 更均匀地设置列宽
    m_operatorTable->setColumnWidth(0, 60);   // ID
    m_operatorTable->setColumnWidth(1, 100);  // 用户名
    m_operatorTable->setColumnWidth(2, 100);  // 姓名
    m_operatorTable->setColumnWidth(3, 100);  // 角色
    m_operatorTable->setColumnWidth(4, 80);   // 状态
    m_operatorTable->setColumnWidth(5, 130);  // 邮箱
    m_operatorTable->setColumnWidth(6, 130);  // 最后登录时间
    m_operatorTable->setColumnWidth(7, 130);  // 最后登录IP

    // 设置角色过滤下拉框
    m_roleFilterCombo->clear();
    m_roleFilterCombo->addItem("所有角色", -1);
    m_roleFilterCombo->addItem("超级管理员", static_cast<int>(Operator::Role::SuperAdmin));
    m_roleFilterCombo->addItem("管理员", static_cast<int>(Operator::Role::Admin));
    m_roleFilterCombo->addItem("操作员", static_cast<int>(Operator::Role::Operator));
    m_roleFilterCombo->addItem("查看者", static_cast<int>(Operator::Role::Viewer));

    updateButtonStates();
}

void OperatorManagementWidget::setupConnections()
{
    // 功能按钮连接
    connect(m_addButton, &QPushButton::clicked, this, &OperatorManagementWidget::onAdd);
    connect(m_editButton, &QPushButton::clicked, this, &OperatorManagementWidget::onEdit);
    connect(m_deleteButton, &QPushButton::clicked, this, &OperatorManagementWidget::onDelete);
    connect(m_resetPasswordButton, &QPushButton::clicked, this, &OperatorManagementWidget::onResetPassword);
    connect(m_changeStatusButton, &QPushButton::clicked, this, &OperatorManagementWidget::onChangeStatus);
    connect(m_searchButton, &QPushButton::clicked, this, &OperatorManagementWidget::onSearch);

    // 搜索过滤连接
    connect(m_filterButton, &QPushButton::clicked, this, &OperatorManagementWidget::onFilter);
    connect(m_clearFilterButton, &QPushButton::clicked, this, &OperatorManagementWidget::onClearFilter);
    connect(m_usernameSearchEdit, &QLineEdit::textChanged, this, &OperatorManagementWidget::onUsernameSearchChanged);
    connect(m_roleFilterCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OperatorManagementWidget::onRoleFilterChanged);

    // 表格连接
    connect(m_operatorTable, &QTableWidget::cellDoubleClicked, this, &OperatorManagementWidget::onTableDoubleClicked);
    connect(m_operatorTable, &QTableWidget::itemSelectionChanged, this, &OperatorManagementWidget::onTableSelectionChanged);

    // 搜索框回车键
    connect(m_usernameSearchEdit, &QLineEdit::returnPressed, this, &OperatorManagementWidget::onFilter);
}

void OperatorManagementWidget::setupTableHeaders()
{
    QStringList headers;
    headers << "ID" << "用户名" << "姓名" << "角色" << "状态" << "邮箱" << "最后登录时间" << "最后登录IP";

    m_operatorTable->setColumnCount(headers.size());
    m_operatorTable->setHorizontalHeaderLabels(headers);
}

void OperatorManagementWidget::loadOperators()
{
    qDebug() << "OperatorManagementWidget::loadOperators: Starting to load operators...";
    try {
        // 只加载管理员和操作员角色的用户（角色 <= 2）
        m_allOperators = m_operatorDao->getOperators();
        qDebug() << "OperatorManagementWidget::loadOperators: Retrieved" << m_allOperators.size() << "operators from database";
        m_filteredOperators = m_allOperators;
        populateOperatorTable(m_filteredOperators);
        updateStatusBar();
        m_statusLabel->setText("操作员数据加载完成");
        qDebug() << "OperatorManagementWidget::loadOperators: Operator loading completed successfully";
    } catch (const std::exception& e) {
        qDebug() << "Load operators failed:" << e.what();
        m_statusLabel->setText("加载操作员数据失败");
        QMessageBox::critical(this, "错误", "加载操作员数据失败：" + QString(e.what()));
    }
}

void OperatorManagementWidget::refreshOperatorTable()
{
    loadOperators();
}

void OperatorManagementWidget::populateOperatorTable(const std::vector<Operator>& operators)
{
    m_operatorTable->setRowCount(0);
    m_operatorTable->setRowCount(operators.size());

    for (size_t i = 0; i < operators.size(); ++i) {
        fillOperatorRow(i, operators[i]);
    }

    updateStatusBar();
}

void OperatorManagementWidget::fillOperatorRow(int row, const Operator& user)
{
    // ID
    m_operatorTable->setItem(row, 0, new QTableWidgetItem(QString::number(user.id())));

    // 用户名
    m_operatorTable->setItem(row, 1, new QTableWidgetItem(user.username()));

    // 姓名
    m_operatorTable->setItem(row, 2, new QTableWidgetItem(user.realName()));

    // 角色
    m_operatorTable->setItem(row, 3, new QTableWidgetItem(getRoleText(user.role())));

    // 状态
    m_operatorTable->setItem(row, 4, new QTableWidgetItem(getStatusText(user.status())));

    // 邮箱
    m_operatorTable->setItem(row, 5, new QTableWidgetItem(user.email()));

    // 最后登录时间
    m_operatorTable->setItem(row, 6, new QTableWidgetItem(formatDate(user.lastLoginAt())));

    // 最后登录IP
    m_operatorTable->setItem(row, 7, new QTableWidgetItem(user.lastLoginIp()));

    // 存储用户ID到行数据中
    m_operatorTable->item(row, 0)->setData(Qt::UserRole, user.id());
}

QString OperatorManagementWidget::getRoleText(Operator::Role role)
{
    switch (role) {
        case Operator::Role::SuperAdmin: return "超级管理员";
        case Operator::Role::Admin: return "管理员";
        case Operator::Role::Operator: return "操作员";
        case Operator::Role::Viewer: return "查看者";
        default: return "未知";
    }
}

QString OperatorManagementWidget::getStatusText(Operator::Status status)
{
    switch (status) {
        case Operator::Status::Active: return "正常";
        case Operator::Status::Inactive: return "停用";
        case Operator::Status::Locked: return "锁定";
        case Operator::Status::Expired: return "过期";
        default: return "未知";
    }
}

QString OperatorManagementWidget::formatDate(const QDateTime& dateTime)
{
    if (!dateTime.isValid()) {
        return "从未登录";
    }
    return dateTime.toString("yyyy-MM-dd HH:mm");
}

void OperatorManagementWidget::updateStatusBar()
{
    m_totalLabel->setText(QString("共 %1 个操作员").arg(m_filteredOperators.size()));
}

void OperatorManagementWidget::updateButtonStates()
{
    bool hasSelection = !m_operatorTable->selectedItems().isEmpty();
    m_editButton->setEnabled(hasSelection && m_operatorTable->selectedItems().size() <= m_operatorTable->columnCount());
    m_deleteButton->setEnabled(hasSelection);
    m_resetPasswordButton->setEnabled(hasSelection);
    m_changeStatusButton->setEnabled(hasSelection);
}

// 功能按钮实现
void OperatorManagementWidget::onAdd()
{
    m_statusLabel->setText("添加操作员功能待实现");
    QMessageBox::information(this, "提示", "添加操作员功能正在开发中...");
}

void OperatorManagementWidget::onEdit()
{
    auto selectedOperators = getSelectedOperators();
    if (selectedOperators.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要编辑的操作员！");
        return;
    }

    if (selectedOperators.size() > 1) {
        QMessageBox::warning(this, "警告", "一次只能编辑一个操作员，请选择单个操作员！");
        return;
    }

    m_statusLabel->setText("编辑操作员功能待实现");
    QMessageBox::information(this, "提示", QString("编辑操作员：%1 功能正在开发中...").arg(selectedOperators[0].username()));
}

void OperatorManagementWidget::onDelete()
{
    auto selectedOperators = getSelectedOperators();
    if (selectedOperators.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要删除的操作员！");
        return;
    }

    QString message;
    if (selectedOperators.size() == 1) {
        message = QString("确定要删除操作员 \"%1\" 吗？").arg(selectedOperators[0].username());
    } else {
        message = QString("确定要删除选中的 %1 个操作员吗？").arg(selectedOperators.size());
    }

    int ret = QMessageBox::question(this, "确认删除", message,
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_statusLabel->setText("删除操作员功能待实现");
        QMessageBox::information(this, "提示", "删除操作员功能正在开发中...");
    }
}

void OperatorManagementWidget::onResetPassword()
{
    auto selectedOperators = getSelectedOperators();
    if (selectedOperators.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要重置密码的操作员！");
        return;
    }

    QString message;
    if (selectedOperators.size() == 1) {
        message = QString("确定要重置操作员 \"%1\" 的密码吗？").arg(selectedOperators[0].username());
    } else {
        message = QString("确定要重置选中的 %1 个操作员的密码吗？").arg(selectedOperators.size());
    }

    int ret = QMessageBox::question(this, "确认重置密码", message,
                                   QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        m_statusLabel->setText("重置密码功能待实现");
        QMessageBox::information(this, "提示", "重置密码功能正在开发中...");
    }
}

void OperatorManagementWidget::onChangeStatus()
{
    auto selectedOperators = getSelectedOperators();
    if (selectedOperators.empty()) {
        QMessageBox::warning(this, "警告", "请先选择要更改状态的操作员！");
        return;
    }

    m_statusLabel->setText("更改操作员状态功能待实现");
    QMessageBox::information(this, "提示", "更改操作员状态功能正在开发中...");
}

void OperatorManagementWidget::onSearch()
{
    onFilter();
}

// 搜索筛选实现
void OperatorManagementWidget::onFilter()
{
    QString username = m_usernameSearchEdit->text().trimmed();
    int roleFilter = m_roleFilterCombo->currentData().toInt();

    if (username.isEmpty() && roleFilter == -1) {
        // 没有筛选条件，显示所有操作员
        m_filteredOperators = m_allOperators;
    } else {
        // 有筛选条件，调用数据库查询
        try {
            m_filteredOperators = m_operatorDao->getOperatorsWithFilter(username, roleFilter);
            m_statusLabel->setText("筛选完成");
        } catch (const std::exception& e) {
            qDebug() << "Filter operators failed:" << e.what();
            m_statusLabel->setText("筛选失败");
            QMessageBox::critical(this, "错误", QString("筛选操作员时发生错误：%1").arg(e.what()));
            return;
        }
    }

    populateOperatorTable(m_filteredOperators);
}

void OperatorManagementWidget::onClearFilter()
{
    m_usernameSearchEdit->clear();
    m_roleFilterCombo->setCurrentIndex(0);

    m_filteredOperators = m_allOperators;
    populateOperatorTable(m_filteredOperators);
    m_statusLabel->setText("已清除筛选条件");
}

void OperatorManagementWidget::onUsernameSearchChanged()
{
    // 实时搜索可以在这里实现，现在先留空
}

void OperatorManagementWidget::onRoleFilterChanged()
{
    // 角色变化时可以自动筛选，现在先留空
}

// 表格操作实现
void OperatorManagementWidget::onTableDoubleClicked(int row, int column)
{
    Q_UNUSED(column)

    if (row >= 0 && row < m_operatorTable->rowCount()) {
        // 双击编辑操作员
        onEdit();
    }
}

void OperatorManagementWidget::onTableSelectionChanged()
{
    updateButtonStates();
}

// 辅助方法实现
std::vector<Operator> OperatorManagementWidget::getSelectedOperators()
{
    std::vector<Operator> selectedOperators;

    QSet<int> selectedRows;
    for (const QTableWidgetItem* item : m_operatorTable->selectedItems()) {
        selectedRows.insert(item->row());
    }

    for (int row : selectedRows) {
        QTableWidgetItem* idItem = m_operatorTable->item(row, 0);
        if (idItem) {
            int userId = idItem->data(Qt::UserRole).toInt();
            for (const Operator& user : m_filteredOperators) {
                if (user.id() == userId) {
                    selectedOperators.push_back(user);
                    break;
                }
            }
        }
    }

    return selectedOperators;
}

Operator* OperatorManagementWidget::getCurrentOperator()
{
    auto selectedOperators = getSelectedOperators();
    if (selectedOperators.empty()) {
        return nullptr;
    }

    // 返回第一个选中的操作员
    for (auto& user : m_filteredOperators) {
        if (user.id() == selectedOperators[0].id()) {
            return &user;
        }
    }

    return nullptr;
}

} // namespace AccessControl