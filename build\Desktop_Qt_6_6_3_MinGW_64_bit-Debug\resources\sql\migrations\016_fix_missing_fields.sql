-- 迁移文件：016_fix_missing_fields.sql
-- 描述：修复缺失的数据库字段
-- 创建时间：2025-08-01
-- 作者：AI Assistant

-- ========== 修复areas表，添加full_path字段 ==========

-- 检查并添加full_path字段
ALTER TABLE areas ADD COLUMN full_path TEXT;

-- 更新现有记录的full_path字段
UPDATE areas SET full_path = area_name WHERE full_path IS NULL OR full_path = '';

-- 为有父级的区域更新完整路径
UPDATE areas SET full_path = (
    SELECT CASE 
        WHEN p.full_path IS NULL OR p.full_path = '' THEN p.area_name || '/' || areas.area_name
        ELSE p.full_path || '/' || areas.area_name
    END
    FROM areas p 
    WHERE p.id = areas.parent_id
) WHERE parent_id IS NOT NULL;

-- ========== 创建触发器自动维护full_path字段 ==========

-- 删除旧触发器（如果存在）
DROP TRIGGER IF EXISTS trigger_areas_update_full_path;

-- 创建触发器，在插入或更新时自动计算full_path
CREATE TRIGGER trigger_areas_update_full_path
    AFTER INSERT ON areas
    FOR EACH ROW
BEGIN
    UPDATE areas SET full_path = (
        CASE 
            WHEN NEW.parent_id IS NULL THEN NEW.area_name
            ELSE (
                SELECT CASE 
                    WHEN p.full_path IS NULL OR p.full_path = '' THEN p.area_name || '/' || NEW.area_name
                    ELSE p.full_path || '/' || NEW.area_name
                END
                FROM areas p 
                WHERE p.id = NEW.parent_id
            )
        END
    ) WHERE id = NEW.id;
END;

-- 创建更新触发器
CREATE TRIGGER trigger_areas_update_full_path_on_update
    AFTER UPDATE ON areas
    FOR EACH ROW
    WHEN OLD.area_name != NEW.area_name OR OLD.parent_id != NEW.parent_id
BEGIN
    -- 更新当前记录的full_path
    UPDATE areas SET full_path = (
        CASE 
            WHEN NEW.parent_id IS NULL THEN NEW.area_name
            ELSE (
                SELECT CASE 
                    WHEN p.full_path IS NULL OR p.full_path = '' THEN p.area_name || '/' || NEW.area_name
                    ELSE p.full_path || '/' || NEW.area_name
                END
                FROM areas p 
                WHERE p.id = NEW.parent_id
            )
        END
    ) WHERE id = NEW.id;
    
    -- 更新所有子记录的full_path
    UPDATE areas SET full_path = (
        SELECT CASE 
            WHEN p.full_path IS NULL OR p.full_path = '' THEN p.area_name || '/' || areas.area_name
            ELSE p.full_path || '/' || areas.area_name
        END
        FROM areas p 
        WHERE p.id = areas.parent_id
    ) WHERE parent_id = NEW.id;
END;
