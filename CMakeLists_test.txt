cmake_minimum_required(VERSION 3.16)
project(AccessControlTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Sql)

# 创建测试可执行文件
add_executable(test_fixes test_fixes.cpp)

# 链接Qt库
target_link_libraries(test_fixes Qt6::Core Qt6::Sql)

# 设置输出目录
set_target_properties(test_fixes PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
) 