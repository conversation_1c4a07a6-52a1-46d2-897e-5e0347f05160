#ifndef OPERATORBIOMETRICDAO_H
#define OPERATORBIOMETRICDAO_H

#include "../IDatabaseProvider.h"
#include "../../models/OperatorBiometric.h"
#include <QList>
#include <QVariant>
#include <memory>

namespace AccessControl {

/**
 * @brief 系统操作员生物识别数据访问对象
 * 负责系统操作员生物识别信息的数据库操作
 */
class OperatorBiometricDao {
public:
    explicit OperatorBiometricDao(std::shared_ptr<IDatabaseProvider> provider);
    ~OperatorBiometricDao() = default;

    // ========== 表管理 ==========

    /**
     * @brief 创建操作员生物识别表
     * @return 创建是否成功
     */
    bool createTable();

    /**
     * @brief 检查操作员生物识别表是否存在
     * @return 表是否存在
     */
    bool tableExists();

    /**
     * @brief 初始化表结构
     * @return 初始化是否成功
     */
    bool initializeTable();

    // ========== CRUD 操作 ==========

    /**
     * @brief 创建操作员生物识别信息
     * @param biometric 操作员生物识别对象
     * @return 创建是否成功，成功时会设置ID
     */
    bool createOperatorBiometric(OperatorBiometric& biometric);

    /**
     * @brief 根据ID获取操作员生物识别信息
     * @param biometricId 生物识别ID
     * @return 操作员生物识别对象
     */
    OperatorBiometric getOperatorBiometricById(int biometricId);

    /**
     * @brief 根据操作员ID获取操作员生物识别信息
     * @param operatorId 操作员ID
     * @return 操作员生物识别对象
     */
    OperatorBiometric getOperatorBiometricByOperatorId(int operatorId);

    /**
     * @brief 更新操作员生物识别信息
     * @param biometric 操作员生物识别对象
     * @return 更新是否成功
     */
    bool updateOperatorBiometric(const OperatorBiometric& biometric);

    /**
     * @brief 删除操作员生物识别信息
     * @param biometricId 生物识别ID
     * @return 删除是否成功
     */
    bool deleteOperatorBiometric(int biometricId);

    /**
     * @brief 根据操作员ID删除操作员生物识别信息
     * @param operatorId 操作员ID
     * @return 删除是否成功
     */
    bool deleteOperatorBiometricByOperatorId(int operatorId);

    /**
     * @brief 获取所有操作员生物识别信息
     * @return 操作员生物识别列表
     */
    QList<OperatorBiometric> getAllOperatorBiometrics();

    // ========== 照片管理 ==========

    /**
     * @brief 更新操作员照片
     * @param operatorId 操作员ID
     * @param photoPath 照片路径
     * @param photoData 照片数据
     * @return 更新是否成功
     */
    bool updateOperatorPhoto(int operatorId, const QString& photoPath, const QByteArray& photoData);

    /**
     * @brief 获取操作员照片数据
     * @param operatorId 操作员ID
     * @return 照片数据
     */
    QByteArray getOperatorPhotoData(int operatorId);

    /**
     * @brief 获取操作员照片路径
     * @param operatorId 操作员ID
     * @return 照片路径
     */
    QString getOperatorPhotoPath(int operatorId);

    /**
     * @brief 删除操作员照片
     * @param operatorId 操作员ID
     * @return 删除是否成功
     */
    bool deleteOperatorPhoto(int operatorId);

    // ========== 指纹管理 ==========

    /**
     * @brief 更新操作员指纹模板
     * @param operatorId 操作员ID
     * @param templateData 指纹模板数据
     * @return 更新是否成功
     */
    bool updateOperatorFingerprint(int operatorId, const QByteArray& templateData);

    /**
     * @brief 获取操作员指纹模板
     * @param operatorId 操作员ID
     * @return 指纹模板数据
     */
    QByteArray getOperatorFingerprintTemplate(int operatorId);

    /**
     * @brief 删除操作员指纹模板
     * @param operatorId 操作员ID
     * @return 删除是否成功
     */
    bool deleteOperatorFingerprint(int operatorId);

    // ========== 人脸管理 ==========

    /**
     * @brief 更新操作员人脸模板
     * @param operatorId 操作员ID
     * @param templateData 人脸模板数据
     * @return 更新是否成功
     */
    bool updateOperatorFace(int operatorId, const QByteArray& templateData);

    /**
     * @brief 获取操作员人脸模板
     * @param operatorId 操作员ID
     * @return 人脸模板数据
     */
    QByteArray getOperatorFaceTemplate(int operatorId);

    /**
     * @brief 删除操作员人脸模板
     * @param operatorId 操作员ID
     * @return 删除是否成功
     */
    bool deleteOperatorFace(int operatorId);

    // ========== 查询和搜索 ==========

    /**
     * @brief 根据生物识别类型获取操作员列表
     * @param type 生物识别类型
     * @return 操作员ID列表
     */
    QList<int> getOperatorsWithBiometricType(OperatorBiometric::BiometricType type);

    /**
     * @brief 搜索有生物识别数据的操作员
     * @param hasPhoto 是否有照片
     * @param hasFingerprint 是否有指纹
     * @param hasFace 是否有人脸
     * @return 操作员ID列表
     */
    QList<int> searchOperatorsWithBiometric(bool hasPhoto = false,
                                           bool hasFingerprint = false,
                                           bool hasFace = false);

    // ========== 统计信息 ==========

    /**
     * @brief 获取操作员生物识别信息总数
     * @return 总数
     */
    int getOperatorBiometricCount();

    /**
     * @brief 获取有生物识别数据的操作员数
     * @return 操作员数
     */
    int getOperatorsWithBiometricCount();

    /**
     * @brief 获取生物识别类型分布
     * @return 类型分布映射
     */
    QMap<QString, int> getBiometricTypeDistribution();

    // ========== 批量操作 ==========

    /**
     * @brief 批量创建操作员生物识别信息
     * @param biometrics 操作员生物识别列表
     * @return 成功创建的数量
     */
    int batchCreateOperatorBiometrics(const QList<OperatorBiometric>& biometrics);

    /**
     * @brief 批量更新操作员生物识别信息
     * @param biometrics 操作员生物识别列表
     * @return 成功更新的数量
     */
    int batchUpdateOperatorBiometrics(const QList<OperatorBiometric>& biometrics);

private:
    std::shared_ptr<IDatabaseProvider> m_provider;

    /**
     * @brief 从查询结果构建操作员生物识别对象
     * @param query 查询结果
     * @return 操作员生物识别对象
     */
    OperatorBiometric buildOperatorBiometricFromQuery(const QSqlQuery& query);

    /**
     * @brief 获取操作员生物识别表的SQL创建语句
     * @return SQL语句
     */
    QString getOperatorBiometricTableSQL();

    /**
     * @brief 获取插入操作员生物识别信息的SQL语句
     * @return SQL语句
     */
    QString getInsertOperatorBiometricSQL();

    /**
     * @brief 获取更新操作员生物识别信息的SQL语句
     * @return SQL语句
     */
    QString getUpdateOperatorBiometricSQL();

    /**
     * @brief 检查数据库提供者是否有效
     * @return 是否有效
     */
    bool isProviderValid();
};

} // namespace AccessControl

#endif // OPERATORBIOMETRICDAO_H