-- 迁移文件：015_fix_database_schema.sql
-- 描述：彻底修复数据库架构问题
-- 创建时间：2025-08-01
-- 作者：AI Assistant

-- ========== 修复areas表 ==========

-- 删除现有的areas表（如果存在）
DROP TABLE IF EXISTS areas;

-- 重新创建areas表，确保包含full_path字段
CREATE TABLE areas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    area_name VARCHAR(100) NOT NULL,              -- 区域名称
    area_code VARCHAR(20) NOT NULL UNIQUE,       -- 区域代码（唯一）
    parent_id INTEGER,                           -- 上级区域ID（NULL表示顶级区域）
    description TEXT,                            -- 区域描述
    enabled INTEGER NOT NULL DEFAULT 1,         -- 状态：1=启用，0=禁用
    sort_order INTEGER NOT NULL DEFAULT 0,      -- 排序顺序
    level_depth INTEGER NOT NULL DEFAULT 1,     -- 区域层级
    full_path TEXT,                              -- 完整路径（如：总区域\华东区\上海）
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,               -- 创建时间
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,               -- 更新时间
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_areas_parent_id ON areas(parent_id);
CREATE INDEX IF NOT EXISTS idx_areas_code ON areas(area_code);
CREATE INDEX IF NOT EXISTS idx_areas_enabled ON areas(enabled);
CREATE INDEX IF NOT EXISTS idx_areas_sort_order ON areas(sort_order);
CREATE INDEX IF NOT EXISTS idx_areas_name ON areas(area_name);

-- 插入示例数据
INSERT INTO areas (area_name, area_code, parent_id, description, enabled, sort_order, level_depth, full_path, created_at, updated_at) VALUES
('总区域', 'ALL', NULL, '所有区域的根节点', 1, 1, 1, '总区域', datetime('now'), datetime('now')),
('华东区', 'EAST', 1, '华东地区', 1, 1, 2, '总区域\华东区', datetime('now'), datetime('now')),
('华北区', 'NORTH', 1, '华北地区', 1, 2, 2, '总区域\华北区', datetime('now'), datetime('now')),
('华南区', 'SOUTH', 1, '华南地区', 1, 3, 2, '总区域\华南区', datetime('now'), datetime('now')),
('上海', 'SH', 2, '上海市', 1, 1, 3, '总区域\华东区\上海', datetime('now'), datetime('now')),
('北京', 'BJ', 3, '北京市', 1, 1, 3, '总区域\华北区\北京', datetime('now'), datetime('now')),
('广州', 'GZ', 4, '广州市', 1, 1, 3, '总区域\华南区\广州', datetime('now'), datetime('now'));

-- ========== 修复controller_doors表 ==========

-- 删除现有的controller_doors表（如果存在）
DROP TABLE IF EXISTS controller_doors;

-- 重新创建controller_doors表，确保包含所有必要字段
CREATE TABLE controller_doors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    controller_id INTEGER NOT NULL,             -- 控制器ID
    door_index INTEGER NOT NULL,                -- 门索引（0-3）
    door_name VARCHAR(50) NOT NULL,             -- 门名称
    enabled INTEGER NOT NULL DEFAULT 1,         -- 是否启用
    control_mode INTEGER NOT NULL DEFAULT 0,    -- 控制方式：0-在线，1-常开，2-常闭
    open_delay INTEGER NOT NULL DEFAULT 3,      -- 开门延时(秒)
    door_icon INTEGER NOT NULL DEFAULT 0,       -- 门图标
    
    -- 读卡器配置
    entry_reader_enabled INTEGER NOT NULL DEFAULT 1,   -- 进门读卡器启用
    exit_reader_enabled INTEGER NOT NULL DEFAULT 1,    -- 出门读卡器启用
    entry_reader_type VARCHAR(50) NOT NULL DEFAULT '进门',  -- 进门读卡器类型
    exit_reader_type VARCHAR(50) NOT NULL DEFAULT '出门',   -- 出门读卡器类型
    entry_attendance INTEGER NOT NULL DEFAULT 0,       -- 进门读卡器作考勤
    exit_attendance INTEGER NOT NULL DEFAULT 0,        -- 出门读卡器作考勤
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE,
    UNIQUE(controller_id, door_index)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_controller_doors_controller_id ON controller_doors(controller_id);
CREATE INDEX IF NOT EXISTS idx_controller_doors_door_index ON controller_doors(door_index);

-- 创建触发器，在更新controller_doors时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_controller_doors_update 
    AFTER UPDATE ON controller_doors
    FOR EACH ROW
BEGIN
    UPDATE controller_doors SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END; 