#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QStackedWidget>
#include <QListWidget>
#include <QListWidgetItem>
#include <QToolBar>
#include <QStatusBar>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QLineEdit>
#include <QTimer>
#include <QMenu>
#include <QMenuBar>
#include <QAction>
#include <QSettings>
#include <QSplitter>
#include <QFrame>
#include <QTabWidget>
#include <QTableWidget>
#include <QTreeWidget>
#include <QDateTimeEdit>
#include <QRadioButton>
#include <QCheckBox>
#include <QFormLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QTextEdit>
#include <QMouseEvent>
#include <QEnterEvent>
#include <memory>

#include "../database/IDatabaseProvider.h"
#include "../config/DatabaseConfig.h"
#include "LoginWindow.h"
#include "DepartmentManagementWidget.h"
#include "AreaManagementWidget.h"
#include "AutoLoginDialog.h"
#include "ConsumerManagementWidget.h"
#include "ControllerDialog.h"

namespace AccessControl {

// 前向声明
class AutoLoginDialog;
class Operator;

/**
 * @brief 专业智能门禁管理系统主窗口
 * 实现三区域布局：标题菜单栏、功能区域、状态栏
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(const Operator& currentOperator, std::shared_ptr<IDatabaseProvider> dbProvider, QWidget *parent = nullptr);
    ~MainWindow();

public slots:
    /**
     * @brief 显示主窗口
     */
    void showMainWindow();

    /**
     * @brief 注销当前用户
     */
    void logout();

protected:
    void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event) override;


private slots:
    // 导航相关
    void onNavigationItemClicked(QListWidgetItem *item);
    void onSubTabChanged(int index);

    // 菜单相关
    void onHamburgerMenuClicked();
    void showFileMenu();
    void showSettingsMenu();
    void showToolsMenu();
    void showHelpMenu();

    // 文件菜单功能
    void viewLogs();
    void backupDatabase();
    void exitApplication();

    // 设置菜单功能
    void fieldReplacement();
    void scheduledTasks();
    void languageSelection();
    void skinSettings();
    void extensionFeatures();

    // 工具菜单功能
    void operatorManagement();
    void changeCredentials();
    void autoLogin();

    // 页面功能
    void openAreaManagement();
    void onControllerManualAdd();
    void onControllerModify();
    void loadControllerData();
    void refreshControllerTable();

    // 帮助菜单功能
    void userManual();
    void softwareUpgrade();
    void feedbackReport();
    void aboutDialog();

    // 窗口控制
    void minimizeWindow();
    void maximizeWindow();
    void closeWindow();

    // 状态更新
    void updateStatusBar();
    void updateDateTime();

private:
    // 核心数据
    Operator m_currentOperator;
    std::shared_ptr<IDatabaseProvider> m_databaseProvider;
    QSettings* m_settings;

    // ========== 主布局组件 ==========
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;

    // ========== 标题菜单栏区域 ==========
    QFrame* m_titleBarFrame;
    QHBoxLayout* m_titleBarLayout;

    // 左侧：软件信息
    QLabel* m_appIconLabel;
    QLabel* m_appNameLabel;

    // 右侧：菜单和控制
    QPushButton* m_hamburgerButton;
    QPushButton* m_minimizeButton;
    QPushButton* m_maximizeButton;
    QPushButton* m_closeButton;

    // 汉堡菜单
    QMenu* m_hamburgerMenu;
    QMenu* m_fileMenu;
    QMenu* m_settingsMenu;
    QMenu* m_toolsMenu;
    QMenu* m_helpMenu;

    // ========== 功能区域 ==========
    QFrame* m_contentFrame;
    QHBoxLayout* m_contentLayout;

    // 左侧导航栏
    QFrame* m_navigationFrame;
    QVBoxLayout* m_navigationLayout;
    QListWidget* m_navigationList;

    // 右侧内容区
    QFrame* m_workFrame;
    QVBoxLayout* m_workLayout;
    QStackedWidget* m_stackedWidget;

    // 各功能页面的标签栏
    QTabWidget* m_basicSettingsTab;    // 基本设置子功能
    // QTabWidget* m_consoleTab;          // 控制台子功能（已移除标签页）
    QTabWidget* m_attendanceTab;       // 考勤子功能
    QTabWidget* m_elevatorTab;         // 电梯管理子功能
    QTabWidget* m_patrolTab;           // 巡检子功能
    QTabWidget* m_diningTab;           // 定额就餐子功能
    QTabWidget* m_meetingTab;          // 会议签到子功能

    // ========== 状态栏区域 ==========
    QFrame* m_statusBarFrame;
    QHBoxLayout* m_statusBarLayout;

    // 左侧状态信息
    QLabel* m_userInfoLabel;
    QLabel* m_databaseLabel;
    QLabel* m_versionLabel;

    // 右侧状态信息
    QLabel* m_statusLabel;
    QLabel* m_countLabel;
    QLabel* m_dateTimeLabel;

    // ========== 定时器 ==========
    QTimer* m_dateTimeTimer;
    QTimer* m_statusTimer;
    QTimer* m_mouseTrackTimer;

    // ========== 当前状态 ==========
    QString m_currentModule;
    int m_currentSubIndex;
    bool m_isMaximized;

    // ========== 控制器管理相关 ==========
    QTableWidget* m_controllerTable;

    // ========== 窗口调整大小和拖动相关 ==========
    bool m_resizing;
    bool m_dragging;
    QPoint m_lastMousePos;
    QPoint m_dragStartPos;
    int m_resizeDirection;

    // ========== 功能组件 ==========

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 初始化标题菜单栏
     */
    void initializeTitleBar();

    /**
     * @brief 初始化功能区域
     */
    void initializeContentArea();

    /**
     * @brief 初始化状态栏
     */
    void initializeStatusBar();

    /**
     * @brief 初始化导航栏
     */
    void initializeNavigation();

    /**
     * @brief 初始化各功能页面
     */
    void initializeFunctionPages();

    /**
     * @brief 初始化基本设置页面
     */
    void initializeBasicSettingsPages();

    /**
     * @brief 初始化控制台页面
     */
    void initializeConsolePages();

    /**
     * @brief 初始化记录查询页面
     */
    void initializeRecordQueryPage();

    /**
     * @brief 初始化考勤页面
     */
    void initializeAttendancePages();

    /**
     * @brief 初始化电梯管理页面
     */
    void initializeElevatorPages();

    /**
     * @brief 初始化巡检页面
     */
    void initializePatrolPages();

    /**
     * @brief 初始化定额就餐页面
     */
    void initializeDiningPages();

    /**
     * @brief 初始化会议签到页面
     */
    void initializeMeetingPages();

    /**
     * @brief 初始化指纹管理页面
     */
    void initializeFingerprintPage();

    /**
     * @brief 初始化人脸识别页面
     */
    void initializeFaceRecognitionPage();

    /**
     * @brief 创建页面的辅助方法
     */
    QWidget* createControllerManagementPage();
    QWidget* createDepartmentManagementPage();
    QWidget* createAreaManagementPage();
    QWidget* createAccessUserManagementPage();
    QWidget* createPermissionManagementPage();
    QWidget* createTimeManagementPage();
    QWidget* createPasswordManagementPage();
    QWidget* createScheduledTaskPage();
    QWidget* createRealTimeMonitorPage();
    QWidget* createAttendancePage();
    QWidget* createElevatorPage();
    QWidget* createPatrolPage();
    QWidget* createDiningPage();
    QWidget* createMeetingPage();

    /**
     * @brief 创建汉堡菜单
     */
    void createHamburgerMenu();

    /**
     * @brief 更新用户信息显示
     */
    void updateUserInfo();

    /**
     * @brief 更新数据库信息显示
     */
    void updateDatabaseInfo();

    /**
     * @brief 初始化样式
     */
    void initializeStyles();

    /**
     * @brief 初始化信号连接
     */
    void initializeConnections();

    /**
     * @brief 初始化定时器
     */
    void initializeTimers();

    /**
     * @brief 设置用户权限
     */
    void setupUserPermissions();

    /**
     * @brief 创建导航项目
     */
    QListWidgetItem* createNavigationItem(const QString& text, const QString& icon = QString());

    /**
     * @brief 切换到指定模块
     */
    void switchToModule(const QString& moduleName);

    /**
     * @brief 更新计数信息
     */
    void updateCountInfo();

    /**
     * @brief 更新鼠标光标
     */
    void updateCursor(const QPoint& pos);

    /**
     * @brief 加载窗口设置
     */
    void loadWindowSettings();

    /**
     * @brief 保存窗口设置
     */
    void saveWindowSettings();

    /**
     * @brief 安装事件过滤器
     */
    void installEventFilters();

    // 页面标识常量
    static const QString MODULE_BASIC_SETTINGS;
    static const QString MODULE_CONSOLE;
    static const QString MODULE_RECORD_QUERY;
    static const QString MODULE_ATTENDANCE;
    static const QString MODULE_ELEVATOR;
    static const QString MODULE_PATROL;
    static const QString MODULE_DINING;
    static const QString MODULE_MEETING;
    static const QString MODULE_FINGERPRINT;
    static const QString MODULE_FACE_RECOGNITION;
};

} // namespace AccessControl

#endif // MAINWINDOW_H
