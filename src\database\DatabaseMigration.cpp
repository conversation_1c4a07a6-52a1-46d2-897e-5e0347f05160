#include "DatabaseMigration.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QFile>
#include <QTextStream>

namespace AccessControl {

DatabaseMigration::DatabaseMigration(std::shared_ptr<IDatabaseProvider> dbProvider)
    : m_db<PERSON><PERSON>ider(dbProvider)
{
}

DatabaseMigration::~DatabaseMigration()
{
}

bool DatabaseMigration::executeSqlFile(const QString& sqlFilePath)
{
    if (!m_dbProvider) {
        qWarning() << "DatabaseMigration: Database provider is null";
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QFile file(sqlFilePath);
    if (!file.exists()) {
        qWarning() << "DatabaseMigration: SQL file does not exist:" << sqlFilePath;
        return false;
    }

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "DatabaseMigration: Could not open SQL file:" << sqlFilePath << ", Error:" << file.errorString();
        return false;
    }

    QTextStream in(&file);
    QString sqlScript = in.readAll();
    file.close();

    // 分割并执行SQL语句（简单分割，可能需要更复杂的解析）
    QStringList statements = sqlScript.split(';', Qt::SkipEmptyParts);

    bool success = true;
    for (const QString& statement : statements) {
        QString trimmedStatement = statement.trimmed();
        if (trimmedStatement.isEmpty()) continue;

        QSqlQuery query(db);
        if (!query.exec(trimmedStatement)) {
            qWarning() << "DatabaseMigration: Failed to execute SQL statement:" << trimmedStatement;
            qWarning() << "DatabaseMigration: Error:" << query.lastError().text();
            success = false;
            break;
        }
    }

    return success;
}

bool DatabaseMigration::runMigrations()
{
    if (!m_dbProvider) {
        qWarning() << "DatabaseMigration: Database provider is null";
        return false;
    }

    // 获取迁移文件列表
    std::vector<QString> migrationFiles = getMigrationFiles();

    // 执行每个迁移文件
    for (const QString& filePath : migrationFiles) {
        qDebug() << "DatabaseMigration: Executing migration file:" << filePath;
        if (!executeSqlFile(filePath)) {
            qWarning() << "DatabaseMigration: Failed to execute migration file:" << filePath;
            // 继续执行其他迁移文件，不要中断
        }
    }

    // 创建areas表
    qDebug() << "DatabaseMigration: Creating areas table...";
    if (!createAreasTable()) {
        qWarning() << "DatabaseMigration: Failed to create areas table";
    }

    // 注释：不再使用旧的users表，改用operators和consumers表
    qDebug() << "DatabaseMigration: Skipping legacy users table extensions (using operators and consumers tables instead)";

    // 暂时跳过这些表，专注于登录功能
    qDebug() << "DatabaseMigration: Skipping operator_cards and operator_biometric tables creation";

    // 暂时跳过，因为默认操作员已在SQL文件中创建
    qDebug() << "DatabaseMigration: Skipping admin operator and sample operators creation (created in SQL file)";

    return true;
}

bool DatabaseMigration::tableExists(const QString& tableName)
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);

    // SQLite和PostgreSQL的表存在检查方式不同
    QString driverName = db.driverName();

    if (driverName == "QSQLITE") {
        query.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?");
        query.addBindValue(tableName);
    } else if (driverName == "QPSQL") {
        query.prepare("SELECT tablename FROM pg_tables WHERE tablename=?");
        query.addBindValue(tableName);
    } else {
        // 通用方式
        query.prepare("SELECT 1 FROM " + tableName + " LIMIT 1");
    }

    if (query.exec()) {
        return query.next();
    }

    return false;
}

bool DatabaseMigration::createDepartmentsTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QString driverName = db.driverName();
    QString createTableSql;

    if (driverName == "QSQLITE") {
        // SQLite版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS departments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(20) NOT NULL UNIQUE,
                parent_id INTEGER,
                description TEXT,
                status INTEGER NOT NULL DEFAULT 1,
                sort_order INTEGER NOT NULL DEFAULT 0,
                level INTEGER NOT NULL DEFAULT 0,
                full_path TEXT,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,

                FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
            )
        )";
    } else if (driverName == "QPSQL") {
        // PostgreSQL版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS departments (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(20) NOT NULL UNIQUE,
                parent_id INTEGER,
                description TEXT,
                status INTEGER NOT NULL DEFAULT 1,
                sort_order INTEGER NOT NULL DEFAULT 0,
                level INTEGER NOT NULL DEFAULT 0,
                full_path TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

                FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
            )
        )";
    } else {
        qWarning() << "DatabaseMigration: Unsupported database driver:" << driverName;
        return false;
    }

    // 创建表
    if (!executeSql(createTableSql)) {
        return false;
    }

    // 创建索引
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON departments(parent_id)",
        "CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code)",
        "CREATE INDEX IF NOT EXISTS idx_departments_status ON departments(status)",
        "CREATE INDEX IF NOT EXISTS idx_departments_sort_order ON departments(sort_order)",
        "CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name)"
    };

    for (const QString& indexSql : indexes) {
        if (!executeSql(indexSql)) {
            qWarning() << "DatabaseMigration: Failed to create index:" << indexSql;
        }
    }

    // 插入示例数据
    insertSampleDepartments();

    return true;
}

bool DatabaseMigration::createAreasTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QString driverName = db.driverName();
    QString createTableSql;

    if (driverName == "QSQLITE") {
        // SQLite版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS areas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                area_code VARCHAR(20) UNIQUE NOT NULL,
                area_name VARCHAR(100) NOT NULL,
                parent_id INTEGER,
                level_depth INTEGER DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                description TEXT,
                enabled BOOLEAN DEFAULT 1,
                full_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
            )
        )";
    } else if (driverName == "QPSQL") {
        // PostgreSQL版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS areas (
                id SERIAL PRIMARY KEY,
                area_code VARCHAR(20) UNIQUE NOT NULL,
                area_name VARCHAR(100) NOT NULL,
                parent_id INTEGER,
                level_depth INTEGER DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                description TEXT,
                enabled BOOLEAN DEFAULT TRUE,
                full_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES areas(id) ON DELETE SET NULL
            )
        )";
    } else {
        qWarning() << "DatabaseMigration: Unsupported database driver:" << driverName;
        return false;
    }

    // 创建表
    if (!executeSql(createTableSql)) {
        return false;
    }

    // 创建索引
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_areas_parent ON areas(parent_id)",
        "CREATE INDEX IF NOT EXISTS idx_areas_code ON areas(area_code)",
        "CREATE INDEX IF NOT EXISTS idx_areas_enabled ON areas(enabled)",
        "CREATE INDEX IF NOT EXISTS idx_areas_sort_order ON areas(sort_order)",
        "CREATE INDEX IF NOT EXISTS idx_areas_name ON areas(area_name)"
    };

    for (const QString& indexSql : indexes) {
        if (!executeSql(indexSql)) {
            qWarning() << "DatabaseMigration: Failed to create index:" << indexSql;
        }
    }

    qDebug() << "DatabaseMigration: Areas table created successfully";

    return true;
}

bool DatabaseMigration::executeSql(const QString& sql)
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);
    if (!query.exec(sql)) {
        qWarning() << "DatabaseMigration: Failed to execute SQL:" << query.lastError().text();
        qWarning() << "SQL:" << sql;
        return false;
    }

    return true;
}

bool DatabaseMigration::ensureDefaultAdminUser()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    // 检查是否已存在admin操作员
    QSqlQuery checkQuery(db);
    checkQuery.prepare("SELECT COUNT(*) FROM operators WHERE username = ?");
    checkQuery.addBindValue("admin");

    if (!checkQuery.exec()) {
        qWarning() << "DatabaseMigration: Failed to check admin operator:" << checkQuery.lastError().text();
        return false;
    }

    bool adminExists = false;
    if (checkQuery.next() && checkQuery.value(0).toInt() > 0) {
        adminExists = true;
        qDebug() << "DatabaseMigration: Admin operator exists, updating password hash...";

        // 更新现有admin操作员的密码哈希
        QSqlQuery updateQuery(db);
        updateQuery.prepare("UPDATE operators SET password_hash = ?, salt = ? WHERE username = ?");
        updateQuery.addBindValue("f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b"); // admin+salt的SHA-256哈希值
        updateQuery.addBindValue("00000000-0000-0000-0000-000000000000");
        updateQuery.addBindValue("admin");

        if (!updateQuery.exec()) {
            qWarning() << "DatabaseMigration: Failed to update admin user:" << updateQuery.lastError().text();
            return false;
        }

        qDebug() << "DatabaseMigration: Admin operator password hash updated successfully";
        return true;
    }

    // 创建默认admin操作员
    qDebug() << "DatabaseMigration: Creating default admin operator...";

    QSqlQuery insertQuery(db);
    insertQuery.prepare(
        "INSERT INTO operators (username, password_hash, salt, role, status, real_name, email, created_at, updated_at) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))"
    );

    insertQuery.addBindValue("admin");
    insertQuery.addBindValue("f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b"); // admin+salt的SHA-256哈希值
    insertQuery.addBindValue("00000000-0000-0000-0000-000000000000");
    insertQuery.addBindValue(0); // 超级管理员
    insertQuery.addBindValue(0); // 正常状态
    insertQuery.addBindValue("系统管理员");
    insertQuery.addBindValue("<EMAIL>");

    if (!insertQuery.exec()) {
        qWarning() << "DatabaseMigration: Failed to create admin user:" << insertQuery.lastError().text();
        return false;
    }

    qDebug() << "DatabaseMigration: Default admin user created successfully";
    return true;
}

bool DatabaseMigration::createSampleUsers()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    // 检查是否已存在示例操作员
    QSqlQuery checkQuery(db);
    checkQuery.prepare("SELECT COUNT(*) FROM operators WHERE username LIKE 'user%'");

    if (!checkQuery.exec()) {
        qWarning() << "DatabaseMigration: Failed to check sample operators:" << checkQuery.lastError().text();
        return false;
    }

    if (checkQuery.next() && checkQuery.value(0).toInt() > 0) {
        qDebug() << "DatabaseMigration: Sample operators already exist";
        return true;
    }

    // 创建示例用户数据
    QStringList sampleUsers = {
        "('user001', 'f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b', '00000000-0000-0000-0000-000000000000', 3, 0, '张三', '<EMAIL>', '13800138001', '110101199001011234', 1, 1, '2024-01-01', '2025-12-31', 1, 'U001', 0, datetime('now'), datetime('now'))",
        "('user002', 'f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b', '00000000-0000-0000-0000-000000000000', 3, 0, '李四', '<EMAIL>', '13800138002', '110101199002021234', 1, 1, '2024-01-01', '2025-12-31', 1, 'U002', 1, datetime('now'), datetime('now'))",
        "('user003', 'f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b', '00000000-0000-0000-0000-000000000000', 3, 0, '王五', '<EMAIL>', '13800138003', '110101199003031234', 1, 0, '2024-01-01', '2025-12-31', 2, 'U003', 0, datetime('now'), datetime('now'))",
        "('user004', 'f4efe8fa7456131801f6e16c55d204a2b0e0816c61cbc1bb38d13809e07d676b', '00000000-0000-0000-0000-000000000000', 3, 0, '赵六', '<EMAIL>', '13800138004', '110101199004041234', 0, 1, '2024-01-01', '2025-12-31', 2, 'U004', 1, datetime('now'), datetime('now'))"
    };

    QString insertSql = "INSERT INTO operators (username, password_hash, salt, role, status, real_name, email, "
                       "phone_number, id_number, access_enabled, attendance_enabled, valid_from, valid_until, "
                       "department_id, work_number, shift_work, created_at, updated_at) VALUES ";

    for (int i = 0; i < sampleUsers.size(); ++i) {
        QSqlQuery insertQuery(db);
        QString sql = insertSql + sampleUsers[i];

        if (!insertQuery.exec(sql)) {
            qWarning() << "DatabaseMigration: Failed to create sample operator" << (i+1) << ":" << insertQuery.lastError().text();
            return false;
        }
    }

    qDebug() << "DatabaseMigration: Sample operators created successfully";
    return true;
}

// 第二个executeSqlFile方法实现已移除，避免重复定义

void DatabaseMigration::insertSampleDepartments()
{
    QStringList sampleData = {
        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('总公司', 'COMPANY', NULL, '总公司', 1, 1, 0, '总公司', datetime('now'), datetime('now'))",

        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('技术部', 'TECH', 1, '技术研发部门', 1, 1, 1, '总公司/技术部', datetime('now'), datetime('now'))",

        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('市场部', 'MARKET', 1, '市场营销部门', 1, 2, 1, '总公司/市场部', datetime('now'), datetime('now'))",

        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('人事部', 'HR', 1, '人力资源部门', 1, 3, 1, '总公司/人事部', datetime('now'), datetime('now'))",

        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('开发组', 'DEV', 2, '软件开发小组', 1, 1, 2, '总公司/技术部/开发组', datetime('now'), datetime('now'))",

        "INSERT OR IGNORE INTO departments (name, code, parent_id, description, status, sort_order, level, full_path, created_at, updated_at) VALUES "
        "('测试组', 'TEST', 2, '软件测试小组', 1, 2, 2, '总公司/技术部/测试组', datetime('now'), datetime('now'))"
    };

    for (const QString& insertSql : sampleData) {
        if (!executeSql(insertSql)) {
            qWarning() << "DatabaseMigration: Failed to insert sample data";
        }
    }

    qDebug() << "DatabaseMigration: Sample departments data inserted";
}

bool DatabaseMigration::createUserProfilesTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QString driverName = db.driverName();
    QString createTableSql;

    if (driverName == "QSQLITE") {
        // SQLite版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS operator_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operator_id INTEGER NOT NULL,
                gender INTEGER DEFAULT 0,
                ethnicity TEXT,
                religion TEXT,
                native_place TEXT,
                birth_date TEXT,
                marital_status INTEGER DEFAULT 0,
                political_status TEXT,
                education TEXT,
                work_phone TEXT,
                home_phone TEXT,
                english_name TEXT,
                organization TEXT,
                job_title TEXT,
                skill_level TEXT,
                certificate_name TEXT,
                certificate_number TEXT,
                social_security_number TEXT,
                hire_date TEXT,
                termination_date TEXT,
                email_address TEXT,
                mailing_address TEXT,
                postal_code TEXT,
                remarks TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        )";
    } else if (driverName == "QPSQL") {
        // PostgreSQL版本
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS user_profiles (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                gender INTEGER DEFAULT 0,
                ethnicity VARCHAR(50),
                religion VARCHAR(50),
                native_place VARCHAR(100),
                birth_date DATE,
                marital_status INTEGER DEFAULT 0,
                political_status VARCHAR(50),
                education VARCHAR(50),
                work_phone VARCHAR(20),
                home_phone VARCHAR(20),
                english_name VARCHAR(100),
                organization VARCHAR(200),
                job_title VARCHAR(100),
                skill_level VARCHAR(50),
                certificate_name VARCHAR(100),
                certificate_number VARCHAR(50),
                social_security_number VARCHAR(50),
                hire_date DATE,
                termination_date DATE,
                email_address VARCHAR(200),
                mailing_address TEXT,
                postal_code VARCHAR(10),
                remarks TEXT,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        )";
    } else {
        qWarning() << "DatabaseMigration: Unsupported database driver:" << driverName;
        return false;
    }

    // 创建表
    if (!executeSql(createTableSql)) {
        return false;
    }

    // 创建索引
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_gender ON user_profiles(gender)",
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_education ON user_profiles(education)",
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_organization ON user_profiles(organization)",
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_birth_date ON user_profiles(birth_date)",
        "CREATE INDEX IF NOT EXISTS idx_user_profiles_hire_date ON user_profiles(hire_date)"
    };

    for (const QString& indexSql : indexes) {
        if (!executeSql(indexSql)) {
            qWarning() << "DatabaseMigration: Failed to create index:" << indexSql;
        }
    }

    // 创建唯一约束
    QString uniqueConstraint = "CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_unique_user ON user_profiles(user_id)";
    if (!executeSql(uniqueConstraint)) {
        qWarning() << "DatabaseMigration: Failed to create unique constraint";
    }

    // 创建触发器（仅SQLite）
    if (driverName == "QSQLITE") {
        QString triggerSql = R"(
            CREATE TRIGGER IF NOT EXISTS trigger_user_profiles_updated_at
                AFTER UPDATE ON user_profiles
                FOR EACH ROW
            BEGIN
                UPDATE user_profiles SET updated_at = datetime('now', 'localtime') WHERE id = NEW.id;
            END
        )";
        if (!executeSql(triggerSql)) {
            qWarning() << "DatabaseMigration: Failed to create trigger";
        }
    }

    qDebug() << "DatabaseMigration: User_profiles table created successfully";

    return true;
}

bool DatabaseMigration::columnExists(const QString& tableName, const QString& columnName)
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QString driverName = db.driverName();
    QSqlQuery query(db);

    if (driverName == "QSQLITE") {
        query.prepare("PRAGMA table_info(" + tableName + ")");
        if (query.exec()) {
            while (query.next()) {
                if (query.value("name").toString() == columnName) {
                    return true;
                }
            }
        }
    } else if (driverName == "QPSQL") {
        query.prepare("SELECT column_name FROM information_schema.columns WHERE table_name = ? AND column_name = ?");
        query.addBindValue(tableName);
        query.addBindValue(columnName);
        if (query.exec() && query.next()) {
            return true;
        }
    }

    return false;
}

bool DatabaseMigration::extendOperatorsTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    // 检查并添加新字段（包含缺失的字段）
    QStringList newColumns = {
        "phone_number VARCHAR(20)",
        "id_number VARCHAR(18)",
        "access_enabled INTEGER NOT NULL DEFAULT 1",
        "attendance_enabled INTEGER NOT NULL DEFAULT 1",
        "valid_from DATE",
        "valid_until DATE DEFAULT '2099-12-31'",
        "department_id INTEGER",
        "work_number VARCHAR(50)",
        "shift_work INTEGER NOT NULL DEFAULT 0",
        "real_name VARCHAR(100)",
        "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
    };

    QStringList columnNames = {
        "phone_number", "id_number", "access_enabled", "attendance_enabled",
        "valid_from", "valid_until", "department_id", "work_number", "shift_work",
        "real_name", "updated_at"
    };

    for (int i = 0; i < columnNames.size(); ++i) {
        if (!columnExists("operators", columnNames[i])) {
            QString alterSql = QString("ALTER TABLE operators ADD COLUMN %1").arg(newColumns[i]);
            if (!executeSql(alterSql)) {
                qWarning() << "DatabaseMigration: Failed to add column:" << columnNames[i];
                return false;
            }
            qDebug() << "DatabaseMigration: Added column:" << columnNames[i];
        } else {
            qDebug() << "DatabaseMigration: Column already exists:" << columnNames[i];
        }
    }

    // 更新现有操作员的默认值（包含缺失字段的默认值）
    QStringList updateStatements = {
        "UPDATE operators SET access_enabled = 1 WHERE access_enabled IS NULL",
        "UPDATE operators SET attendance_enabled = 1 WHERE attendance_enabled IS NULL",
        "UPDATE operators SET shift_work = 0 WHERE shift_work IS NULL",
        "UPDATE operators SET valid_from = date('now') WHERE valid_from IS NULL OR valid_from = ''",
        "UPDATE operators SET valid_until = '2099-12-31' WHERE valid_until IS NULL OR valid_until = ''",
        "UPDATE operators SET real_name = username WHERE real_name IS NULL OR real_name = ''",
        "UPDATE operators SET updated_at = created_at WHERE updated_at IS NULL"
    };

    for (const QString& updateSql : updateStatements) {
        if (!executeSql(updateSql)) {
            qWarning() << "DatabaseMigration: Failed to update existing data:" << updateSql;
        }
    }

    return true;
}

bool DatabaseMigration::createOperatorCardsTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QString driverName = db.driverName();
    QString createTableSql;

    if (driverName == "QSQLITE") {
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS operator_cards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operator_id INTEGER REFERENCES operators(id) ON DELETE CASCADE,
                card_number VARCHAR(50) UNIQUE NOT NULL,
                card_type INTEGER NOT NULL DEFAULT 0,
                card_name VARCHAR(100),
                is_physical_card INTEGER NOT NULL DEFAULT 1,
                is_primary_card INTEGER NOT NULL DEFAULT 0,
                status INTEGER NOT NULL DEFAULT 0,
                issue_date DATE,
                expiry_date DATE,
                last_used TIMESTAMP,
                use_count INTEGER DEFAULT 0,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )";
    } else if (driverName == "QPSQL") {
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS operator_cards (
                id SERIAL PRIMARY KEY,
                operator_id INTEGER REFERENCES operators(id) ON DELETE CASCADE,
                card_number VARCHAR(50) UNIQUE NOT NULL,
                card_type INTEGER NOT NULL DEFAULT 0,
                card_name VARCHAR(100),
                is_physical_card INTEGER NOT NULL DEFAULT 1,
                is_primary_card INTEGER NOT NULL DEFAULT 0,
                status INTEGER NOT NULL DEFAULT 0,
                issue_date DATE,
                expiry_date DATE,
                last_used TIMESTAMP,
                use_count INTEGER DEFAULT 0,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )";
    } else {
        qWarning() << "DatabaseMigration: Unsupported database driver:" << driverName;
        return false;
    }

    if (!executeSql(createTableSql)) {
        return false;
    }

    // 创建索引
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_user_cards_user_id ON user_cards(user_id)",
        "CREATE INDEX IF NOT EXISTS idx_user_cards_card_number ON user_cards(card_number)",
        "CREATE INDEX IF NOT EXISTS idx_user_cards_card_type ON user_cards(card_type)",
        "CREATE INDEX IF NOT EXISTS idx_user_cards_status ON user_cards(status)",
        "CREATE INDEX IF NOT EXISTS idx_user_cards_is_primary ON user_cards(is_primary_card)"
    };

    for (const QString& indexSql : indexes) {
        if (!executeSql(indexSql)) {
            qWarning() << "DatabaseMigration: Failed to create index:" << indexSql;
        }
    }

    return true;
}

bool DatabaseMigration::createOperatorBiometricTable()
{
    if (!m_dbProvider) {
        return false;
    }

    QSqlDatabase db = m_dbProvider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "DatabaseMigration: Database is not open";
        return false;
    }

    QString driverName = db.driverName();
    QString createTableSql;

    if (driverName == "QSQLITE") {
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS operator_biometric (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                operator_id INTEGER REFERENCES operators(id) ON DELETE CASCADE,
                photo_path VARCHAR(500),
                photo_data BLOB,
                photo_format VARCHAR(10),
                photo_size INTEGER,
                fingerprint_template BLOB,
                fingerprint_quality INTEGER,
                face_template BLOB,
                face_quality INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )";
    } else if (driverName == "QPSQL") {
        createTableSql = R"(
            CREATE TABLE IF NOT EXISTS operator_biometric (
                id SERIAL PRIMARY KEY,
                operator_id INTEGER REFERENCES operators(id) ON DELETE CASCADE,
                photo_path VARCHAR(500),
                photo_data BYTEA,
                photo_format VARCHAR(10),
                photo_size INTEGER,
                fingerprint_template BYTEA,
                fingerprint_quality INTEGER,
                face_template BYTEA,
                face_quality INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )";
    } else {
        qWarning() << "DatabaseMigration: Unsupported database driver:" << driverName;
        return false;
    }

    if (!executeSql(createTableSql)) {
        return false;
    }

    // 创建索引和唯一约束
    QStringList constraints = {
        "CREATE INDEX IF NOT EXISTS idx_user_biometric_user_id ON user_biometric(user_id)",
        "CREATE UNIQUE INDEX IF NOT EXISTS idx_user_biometric_unique_user ON user_biometric(user_id)"
    };

    for (const QString& constraint : constraints) {
        if (!executeSql(constraint)) {
            qWarning() << "DatabaseMigration: Failed to create constraint:" << constraint;
        }
    }

    return true;
}

std::vector<QString> DatabaseMigration::getMigrationFiles()
{
    std::vector<QString> migrationFiles = {
        ":/resources/sql/migrations/010_create_operators_table.sql",
        ":/resources/sql/migrations/012_create_departments_table.sql",  // 使用新的departments表
        ":/resources/sql/migrations/008_create_consumers_table.sql",
        ":/resources/sql/migrations/011_extend_consumers_table.sql",  // 使用resources版本，包含photo_data字段
        ":/resources/sql/migrations/005_create_controllers_table.sql",
        ":/resources/sql/migrations/006_create_controller_doors_table.sql",
        ":/resources/sql/migrations/007_update_controllers_table.sql",
        ":/resources/sql/migrations/008_add_door_icon_field.sql",
        ":/resources/sql/migrations/013_create_areas_table.sql",      // 使用新的areas表
        ":/resources/sql/migrations/014_fix_controllers_table.sql"    // 修复controllers表
    };
    return migrationFiles;
}

} // namespace AccessControl