#include <QCoreApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QDir>
#include <iostream>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 连接数据库
    QString dbPath = QDir::homePath() + "/AppData/Local/AccessControl/AccessControlSystem/access_control.db";
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(dbPath);
    
    if (!db.open()) {
        qDebug() << "Failed to open database:" << db.lastError().text();
        return -1;
    }
    
    qDebug() << "Database opened successfully";
    
    // 查询控制器和门配置
    QSqlQuery query(db);
    query.prepare("SELECT c.*, cd.door_name, cd.enabled FROM controllers c "
                  "LEFT JOIN controller_doors cd ON c.id = cd.controller_id "
                  "ORDER BY c.controller_number, cd.door_index");
    
    if (query.exec()) {
        int currentControllerId = -1;
        QStringList currentDoorNames;
        
        while (query.next()) {
            int controllerId = query.value("id").toInt();
            QString controllerNumber = query.value("controller_number").toString();
            QString serialNumber = query.value("serial_number").toString();
            QString doorName = query.value("door_name").toString();
            bool doorEnabled = query.value("enabled").toBool();
            
            // 如果是新的控制器，输出上一个控制器的信息
            if (currentControllerId != -1 && currentControllerId != controllerId) {
                QString doorInfo;
                if (currentDoorNames.isEmpty()) {
                    doorInfo = "无启用的门";
                } else {
                    doorInfo = currentDoorNames.join("，");
                }
                qDebug() << "控制器" << controllerNumber << "(" << serialNumber << "):" << doorInfo;
                currentDoorNames.clear();
            }
            
            // 记录当前控制器的门信息
            if (currentControllerId != controllerId) {
                currentControllerId = controllerId;
            }
            
            if (doorEnabled && !doorName.isEmpty()) {
                currentDoorNames.append(doorName);
            }
        }
        
        // 输出最后一个控制器的信息
        if (currentControllerId != -1 && !currentDoorNames.isEmpty()) {
            QString doorInfo = currentDoorNames.join("，");
            qDebug() << "控制器" << query.value("controller_number").toString() 
                     << "(" << query.value("serial_number").toString() << "):" << doorInfo;
        }
    } else {
        qDebug() << "Query failed:" << query.lastError().text();
    }
    
    return 0;
} 