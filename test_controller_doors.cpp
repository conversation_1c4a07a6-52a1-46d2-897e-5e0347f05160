#include <QCoreApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 获取数据库路径
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QString dbPath = dataDir + "/access_control.db";
    qDebug() << "Database path:" << dbPath;
    
    // 连接数据库
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(dbPath);
    
    if (!db.open()) {
        qDebug() << "Failed to open database:" << db.lastError().text();
        return -1;
    }
    
    qDebug() << "Database opened successfully";
    
    // 检查controller_doors表结构
    QSqlQuery query;
    if (query.exec("PRAGMA table_info(controller_doors)")) {
        qDebug() << "\n=== controller_doors table structure ===";
        while (query.next()) {
            QString name = query.value("name").toString();
            QString type = query.value("type").toString();
            bool notNull = query.value("notnull").toBool();
            QString defaultValue = query.value("dflt_value").toString();
            qDebug() << "Column:" << name << "Type:" << type << "NOT NULL:" << notNull << "Default:" << defaultValue;
        }
    } else {
        qDebug() << "Failed to get table info:" << query.lastError().text();
    }
    
    // 检查controllers表数据
    if (query.exec("SELECT id, controller_number, serial_number FROM controllers")) {
        qDebug() << "\n=== controllers table data ===";
        while (query.next()) {
            int id = query.value("id").toInt();
            int controllerNumber = query.value("controller_number").toInt();
            QString serialNumber = query.value("serial_number").toString();
            qDebug() << "Controller ID:" << id << "Number:" << controllerNumber << "Serial:" << serialNumber;
        }
    } else {
        qDebug() << "Failed to query controllers:" << query.lastError().text();
    }
    
    // 检查controller_doors表数据
    if (query.exec("SELECT * FROM controller_doors ORDER BY controller_id, door_index")) {
        qDebug() << "\n=== controller_doors table data ===";
        while (query.next()) {
            int id = query.value("id").toInt();
            int controllerId = query.value("controller_id").toInt();
            int doorIndex = query.value("door_index").toInt();
            QString doorName = query.value("door_name").toString();
            bool enabled = query.value("enabled").toBool();
            int controlMode = query.value("control_mode").toInt();
            int openDelay = query.value("open_delay").toInt();
            int doorIcon = query.value("door_icon").toInt();
            bool entryReaderEnabled = query.value("entry_reader_enabled").toBool();
            bool exitReaderEnabled = query.value("exit_reader_enabled").toBool();
            QString entryReaderType = query.value("entry_reader_type").toString();
            QString exitReaderType = query.value("exit_reader_type").toString();
            bool entryAttendance = query.value("entry_attendance").toBool();
            bool exitAttendance = query.value("exit_attendance").toBool();
            
            qDebug() << "Door ID:" << id 
                     << "Controller ID:" << controllerId 
                     << "Index:" << doorIndex 
                     << "Name:" << doorName 
                     << "Enabled:" << enabled
                     << "Control Mode:" << controlMode
                     << "Open Delay:" << openDelay
                     << "Door Icon:" << doorIcon
                     << "Entry Reader Enabled:" << entryReaderEnabled
                     << "Exit Reader Enabled:" << exitReaderEnabled
                     << "Entry Reader Type:" << entryReaderType
                     << "Exit Reader Type:" << exitReaderType
                     << "Entry Attendance:" << entryAttendance
                     << "Exit Attendance:" << exitAttendance;
        }
    } else {
        qDebug() << "Failed to query controller_doors:" << query.lastError().text();
    }
    
    // 测试插入门配置
    qDebug() << "\n=== Testing door config insertion ===";
    
    // 先获取一个控制器ID
    if (query.exec("SELECT id FROM controllers LIMIT 1")) {
        if (query.next()) {
            int controllerId = query.value("id").toInt();
            qDebug() << "Testing with controller ID:" << controllerId;
            
            // 删除现有的门配置
            QSqlQuery deleteQuery;
            deleteQuery.prepare("DELETE FROM controller_doors WHERE controller_id = ?");
            deleteQuery.addBindValue(controllerId);
            if (deleteQuery.exec()) {
                qDebug() << "Deleted existing door configs for controller" << controllerId;
            }
            
            // 插入4个门的配置
            QSqlQuery insertQuery;
            insertQuery.prepare(
                "INSERT INTO controller_doors ("
                "controller_id, door_index, door_name, enabled, control_mode, open_delay, door_icon, "
                "entry_reader_enabled, exit_reader_enabled, entry_reader_type, exit_reader_type, "
                "entry_attendance, exit_attendance"
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            );
            
            for (int i = 0; i < 4; ++i) {
                insertQuery.bindValue(0, controllerId);
                insertQuery.bindValue(1, i);
                insertQuery.bindValue(2, QString("门%1").arg(i + 1));
                insertQuery.bindValue(3, true);
                insertQuery.bindValue(4, 0);
                insertQuery.bindValue(5, 3);
                insertQuery.bindValue(6, 0);
                insertQuery.bindValue(7, true);
                insertQuery.bindValue(8, true);
                insertQuery.bindValue(9, "进门");
                insertQuery.bindValue(10, "出门");
                insertQuery.bindValue(11, false);
                insertQuery.bindValue(12, false);
                
                if (insertQuery.exec()) {
                    qDebug() << "Successfully inserted door config" << i << "for controller" << controllerId;
                } else {
                    qDebug() << "Failed to insert door config" << i << ":" << insertQuery.lastError().text();
                }
            }
            
            // 验证插入结果
            QSqlQuery verifyQuery;
            verifyQuery.prepare("SELECT door_index, door_name, enabled FROM controller_doors WHERE controller_id = ? ORDER BY door_index");
            verifyQuery.addBindValue(controllerId);
            if (verifyQuery.exec()) {
                qDebug() << "Verification - door configs for controller" << controllerId << ":";
                while (verifyQuery.next()) {
                    int doorIndex = verifyQuery.value("door_index").toInt();
                    QString doorName = verifyQuery.value("door_name").toString();
                    bool enabled = verifyQuery.value("enabled").toBool();
                    qDebug() << "  Door" << doorIndex << ":" << doorName << "Enabled:" << enabled;
                }
            }
        }
    }
    
    db.close();
    return 0;
} 