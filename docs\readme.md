# 门禁控制系统 (Access Control System)

## 项目概述
本项目是一个基于Qt 6和C++开发的专业门禁管理系统，用于管理用户门禁权限、考勤记录、用户信息等。系统支持多种身份验证方式，包括IC/ID卡、CPU卡、指纹识别、人脸识别等。适用于Windows和国产操作系统(包括ARM架构)。系统提供完整的门禁控制、人员管理、考勤管理等功能，支持高性能的数据处理和报表生成，支持多客户端并发访问。

## 系统架构
系统采用MVC架构设计：
- 模型(Models)：定义数据结构和业务逻辑
- 视图(Views)：用户界面组件
- 控制器(Controllers)：处理用户输入并更新模型和视图

### 目录结构
```
/
├── src/                    # 源代码目录
│   ├── models/             # 数据模型
│   ├── views/              # 视图组件
│   ├── controllers/        # 控制器
│   ├── database/           # 数据库相关
│   ├── utils/              # 工具函数
│   ├── config/             # 配置文件
│   └── core/               # 核心功能
├── resources/              # 资源文件
├── docs/                   # 文档
└── build/                  # 构建目录
```

## 主要功能
1. **用户管理**
   - 基本信息管理（工号、姓名、卡号等）
   - 扩展信息管理（性别、民族、籍贯等）
   - 生物识别数据管理（照片、指纹、人脸）
   - 多卡管理（支持多种类型的卡）
   - 表单验证（手机号、身份证号格式验证）
   - USB读卡器支持（多次刷卡自动清空）
   - 全局搜索功能（支持所有字段模糊查询）
   - 隐私保护显示（身份证号脱敏）
   - 数据导入导出（Excel/CSV格式）

2. **部门管理**
   - 多级部门结构
   - 部门信息管理
   - 完整路径显示

3. **区域管理**
   - 区域定义与分级
   - 区域权限管理

4. **门禁控制**
   - 基于时间和区域的访问控制
   - 多种验证方式支持

5. **考勤管理**
   - 考勤记录
   - 考勤报表

6. **数据安全**
   - 隐私信息保护
   - 数据完整性验证
   - 操作日志记录

## 数据库设计
系统使用SQLite数据库，主要表结构包括：

### 核心表结构
- **operators**: 系统登录用户（管理员、操作员）
- **consumers**: 门禁卡持有者（员工、访客等）
- **consumers_extended**: 门禁用户扩展信息
- **consumer_cards**: 门禁用户卡片信息
- **consumer_biometrics**: 门禁用户生物识别数据
- **consumer_photos**: 门禁用户照片信息
- **departments**: 部门信息（支持多级层次结构）
- **areas**: 区域信息（支持多级层次结构）

### 数据库特性
- **用户类型分离**: Operator和Consumer完全独立管理
- **多级层次支持**: 部门和区域支持无限层级，自动维护完整路径
- **完整性约束**: 外键关系确保数据一致性
- **自动时间戳**: 创建和更新时间自动维护
- **索引优化**: 关键字段建立索引，提升查询性能

## 使用技术
- 编程语言: C++
- UI框架: Qt
- 数据库: SQLite
- 身份验证: 支持多种生物识别和卡片识别

## 最近更新
- **2025-07-31**: 区域管理和控制器管理关键问题修复
  - **控制器管理功能全面优化**:
    - 修复控制器列表中"控制的门"字段显示，从显示"X门控制器"改为显示实际的门名称
    - 实现启用门名称的逗号分隔显示（如：门1，门2，门3），支持多门控制器完整显示
    - 当没有启用的门时显示"无启用的门"
    - 添加控制器修改功能，支持选择控制器进行编辑
    - 优化表格行选择模式，点击任意单元格选中整行
    - 确保区域选择下拉框正确显示层级关系（使用"\"分隔符）
    - 添加详细的调试日志，便于问题诊断和功能验证
  - **区域管理保存修复**:
    - 修复区域信息保存失败问题（Parameter count mismatch错误）
    - 完善DatabaseMigration中areas表的创建逻辑
    - 修复AreaDao中SQL语句与数据库表结构不匹配问题
    - 添加level_depth字段支持，确保数据完整性
  - **控制器管理全面优化**:
    - 修复控制器保存失败问题，解决SQL参数不匹配错误
    - 实现控制器列表自动刷新功能，添加成功后立即显示
    - 修复门配置默认设置，所有4个门默认启用
    - 解决输入序列号后门配置自动禁用的问题
    - 添加详细的调试日志，便于问题诊断
  - **区域显示格式优化**:
    - 实现区域层级关系使用"\"分隔符显示
    - 修复区域fullPath数据构建逻辑
    - 统一区域显示格式，提升用户体验
  - **数据库架构完善**:
    - 添加缺失的头文件包含（QSqlRecord、QDateTime）
    - 修复控制器表迁移文件执行问题
    - 简化复杂的字段检查逻辑，提升稳定性

- **2025-07-30**: 控制器管理功能修复与优化
  - **区域层级显示修复**:
    - 修复控制器对话框中区域选择下拉框显示问题，正确显示完整层级关系
    - 使用反斜杠"\"分隔符显示区域完整路径（如：园区\东区\1号楼）
    - 修复控制器列表中区域名称显示，从显示"区域ID"改为显示完整区域路径
  - **控制器列表刷新修复**:
    - 修复控制器添加成功后列表不自动刷新的问题
    - 移除不必要的延迟刷新机制，改为立即刷新确保数据及时显示
    - 添加AreaDao头文件包含，确保区域查询功能正常工作
  - **代码优化**:
    - 完善控制器管理模块的错误处理和日志记录
    - 统一区域显示格式，保持与部门管理模块的一致性

- **2025-07-30**: 用户管理功能全面优化
  - **表单验证增强**:
    - 实现11位手机号格式验证，支持中国大陆常见运营商号段
    - 添加18位身份证号完整校验，包含校验位算法验证
    - 实时验证反馈，清晰的错误提示和自动焦点定位
  - **查询功能修复**:
    - 修复工号、卡号查询失效问题，优化SQL查询逻辑
    - 实现全局搜索功能，支持所有字段模糊查询和Ctrl+F快捷键
    - 创建非模态搜索对话框，支持智能结果定位
  - **USB读卡器功能完善**:
    - 修复用户管理主界面卡号查询框刷卡累加问题
    - 实现智能新输入检测机制，使用定时器区分连续输入
    - 统一所有界面的USB读卡器行为，支持多次刷卡自动清空
  - **界面布局优化**:
    - 调整查询输入框宽度和对齐方式，提升视觉效果
    - 优化全局搜索对话框尺寸（800x300px）
    - 完善部门层级显示，使用反斜杠显示完整路径
  - **隐私保护实现**:
    - 身份证号隐私保护显示（123456********1234格式）
    - 界面显示隐私保护，导出文件保持完整信息
  - **数据导入导出**:
    - 实现Excel/CSV格式的用户数据导出功能
    - 支持批量用户数据导入，包含详细的错误处理和结果统计
    - 自动生成带时间戳的文件名，支持当前筛选数据导出

- **2025-07-26**: 重大架构重构与功能完善
  - **用户类型分离**: 将原来的User类型分离为Operator（系统登录用户）和Consumer（门禁卡持有者），避免概念混淆
  - **数据库架构升级**: 实现operators和consumers两个独立表结构，完善数据库迁移机制
  - **部门管理完善**:
    - 修复departments表缺失问题，重新实现部门数据创建
    - 实现完整的多级部门路径显示，支持无限层级的"/"分隔显示
    - 完善部门完整路径自动计算和更新机制
  - **区域管理修复**: 参照部门管理方案，修复areas表缺失问题
  - **用户对话框增强**:
    - 集成真实数据库数据，移除硬编码部门列表
    - 实现智能照片命名：优先使用人脸卡号，次选主卡号
    - 添加卡片必填验证，确保数据完整性
    - 修复卡片编辑时卡号显示问题
    - 优化卡号输入，支持USB读卡器直接输入
  - **拍照功能完善**:
    - 重写相机对话框，提升稳定性和用户体验
    - 实现证件照比例裁剪（3:4），确保照片规格统一
    - 修复相机预览拉伸问题，优化图像质量

- 2024-06-09: 完成用户管理功能，包括添加用户的非模态对话框设计、照片管理、指纹管理和多卡管理功能

## 开发计划
- [x] 系统基础架构
- [x] 登录功能
- [x] 用户管理完善
  - [x] 添加用户对话框
  - [x] 用户照片管理
  - [x] 用户指纹管理
  - [x] 多卡管理
  - [x] 用户类型重构（Operator/Consumer分离）
  - [x] 智能照片命名系统
  - [x] 卡片必填验证
  - [x] 表单验证增强（手机号、身份证号）
  - [x] USB读卡器功能完善
  - [x] 查询功能修复（工号、卡号查询）
  - [x] 全局搜索功能
  - [x] 隐私保护显示（身份证号）
  - [x] 数据导入导出（Excel/CSV）
- [x] 部门管理
  - [x] 多级部门结构
  - [x] 完整路径显示
  - [x] 自动路径更新
- [x] 区域管理
  - [x] 区域表结构修复
  - [x] 基础区域管理功能
  - [x] 区域信息保存功能修复
  - [x] 区域层级显示格式优化（使用"\"分隔符）
- [x] 控制器管理
  - [x] 控制器基础功能实现
  - [x] 控制器保存功能修复
  - [x] 门配置默认设置优化
  - [x] 控制器列表自动刷新功能
- [ ] 权限管理
- [ ] 考勤管理
- [ ] 报表功能

## 贡献者
- 系统开发团队

## 许可证
- 专有软件，未经授权不得使用