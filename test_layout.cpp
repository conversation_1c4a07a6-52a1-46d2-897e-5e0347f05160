#include <QApplication>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QButtonGroup>
#include <QTextEdit>
#include <QPushButton>
#include <QMessageBox>

class TestControllerDialog : public QDialog
{
    Q_OBJECT

public:
    TestControllerDialog(QWidget *parent = nullptr) : QDialog(parent)
    {
        setWindowTitle("控制器添加对话框 - 布局测试");
        setModal(false);
        resize(800, 600);
        
        initializeUI();
    }

private:
    void initializeUI()
    {
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        mainLayout->setContentsMargins(10, 10, 10, 10);
        mainLayout->setSpacing(10);

        // 基本信息页面
        QWidget* basicInfoTab = new QWidget();
        QFormLayout* basicInfoLayout = new QFormLayout(basicInfoTab);
        basicInfoLayout->setLabelAlignment(Qt::AlignRight);
        basicInfoLayout->setFormAlignment(Qt::AlignLeft | Qt::AlignTop);
        basicInfoLayout->setVerticalSpacing(10);

        // 控制器编号
        QSpinBox* controllerNumberSpinBox = new QSpinBox();
        controllerNumberSpinBox->setRange(1, 9999);
        basicInfoLayout->addRow("控制器编号*:", controllerNumberSpinBox);

        // 序列号SN - 调整为同一行显示，固定宽度
        QHBoxLayout* serialLayout = new QHBoxLayout();
        QLineEdit* serialNumberEdit = new QLineEdit();
        serialNumberEdit->setMaxLength(9);
        serialNumberEdit->setFixedWidth(120); // 固定宽度，适合9位数字
        serialNumberEdit->setPlaceholderText("9位数字");
        
        QLabel* controllerTypeLabel = new QLabel("未知类型");
        controllerTypeLabel->setStyleSheet("color: #666; font-style: italic;");
        QLabel* seriesNameLabel = new QLabel("未知系列");
        seriesNameLabel->setStyleSheet("color: #666; font-style: italic;");
        
        serialLayout->addWidget(serialNumberEdit);
        serialLayout->addWidget(new QLabel("类型:"));
        serialLayout->addWidget(controllerTypeLabel);
        serialLayout->addWidget(new QLabel("系列:"));
        serialLayout->addWidget(seriesNameLabel);
        serialLayout->addStretch(); // 添加弹性空间
        basicInfoLayout->addRow("序列号SN*:", serialLayout);

        // 启用状态 - 紧凑显示
        QCheckBox* enabledCheckBox = new QCheckBox("启用控制器");
        enabledCheckBox->setChecked(true);
        basicInfoLayout->addRow("状态:", enabledCheckBox);

        // 所在区域
        QComboBox* areaComboBox = new QComboBox();
        areaComboBox->addItem("无区域");
        areaComboBox->addItem("一楼");
        areaComboBox->addItem("二楼");
        basicInfoLayout->addRow("所在区域:", areaComboBox);

        // 说明
        QTextEdit* descriptionEdit = new QTextEdit();
        descriptionEdit->setMaximumHeight(60);
        descriptionEdit->setPlaceholderText("请输入说明信息（最多60个字符）");
        basicInfoLayout->addRow("说明:", descriptionEdit);

        // 网络配置 - 移到基本信息中
        QGroupBox* networkGroup = new QGroupBox("网络配置");
        QVBoxLayout* networkLayout = new QVBoxLayout(networkGroup);
        
        // 网络模式选择
        QButtonGroup* networkModeGroup = new QButtonGroup(this);
        
        QHBoxLayout* modeLayout = new QHBoxLayout();
        QRadioButton* lanModeRadio = new QRadioButton("小型局域网[同网段]");
        lanModeRadio->setChecked(true);
        networkModeGroup->addButton(lanModeRadio, 0);
        modeLayout->addWidget(lanModeRadio);
        
        QRadioButton* wanModeRadio = new QRadioButton("中大型局域网[跨网段]或Internet互联网");
        networkModeGroup->addButton(wanModeRadio, 1);
        modeLayout->addWidget(wanModeRadio);
        modeLayout->addStretch();
        
        networkLayout->addLayout(modeLayout);

        // IP和端口配置 - 放在网络模式内
        QGroupBox* wanConfigGroup = new QGroupBox();
        wanConfigGroup->setEnabled(false);
        QHBoxLayout* wanLayout = new QHBoxLayout(wanConfigGroup);
        
        // IP地址
        wanLayout->addWidget(new QLabel("IP地址:"));
        QLineEdit* ipAddressEdit = new QLineEdit();
        ipAddressEdit->setPlaceholderText("*************");
        ipAddressEdit->setFixedWidth(150);
        wanLayout->addWidget(ipAddressEdit);
        
        // 端口号
        wanLayout->addWidget(new QLabel("端口号:"));
        QSpinBox* portSpinBox = new QSpinBox();
        portSpinBox->setRange(1, 65535);
        portSpinBox->setValue(60000);
        portSpinBox->setFixedWidth(80);
        wanLayout->addWidget(portSpinBox);
        wanLayout->addStretch();
        
        networkLayout->addWidget(wanConfigGroup);
        
        basicInfoLayout->addRow(networkGroup);

        // 扩展功能
        QGroupBox* extendedGroup = new QGroupBox("扩展功能");
        QHBoxLayout* extendedLayout = new QHBoxLayout(extendedGroup);
        
        QCheckBox* mobileRemoteCheckBox = new QCheckBox("手机app远程开门");
        mobileRemoteCheckBox->setChecked(true);
        extendedLayout->addWidget(mobileRemoteCheckBox);
        
        QCheckBox* qrCodeCheckBox = new QCheckBox("二维码开门功能");
        qrCodeCheckBox->setChecked(true);
        extendedLayout->addWidget(qrCodeCheckBox);
        extendedLayout->addStretch();
        
        basicInfoLayout->addRow(extendedGroup);

        // 门配置 - 移到基本信息中
        QGroupBox* doorGroup = new QGroupBox("门配置");
        QVBoxLayout* doorLayout = new QVBoxLayout(doorGroup);

        // 创建2个门的配置界面作为示例
        for (int i = 0; i < 2; ++i) {
            QGroupBox* singleDoorGroup = new QGroupBox(QString("门%1").arg(i + 1));
            QFormLayout* singleDoorLayout = new QFormLayout(singleDoorGroup);
            singleDoorLayout->setVerticalSpacing(8);

            // 门名称与状态同一行
            QHBoxLayout* nameStatusLayout = new QHBoxLayout();
            QLineEdit* nameEdit = new QLineEdit();
            nameEdit->setText(QString("门%1").arg(i + 1));
            nameEdit->setFixedWidth(100);
            
            QCheckBox* enabledCheckBox = new QCheckBox("启用此门");
            enabledCheckBox->setChecked(i == 0); // 默认只启用第一个门
            
            nameStatusLayout->addWidget(new QLabel("门名称:"));
            nameStatusLayout->addWidget(nameEdit);
            nameStatusLayout->addWidget(enabledCheckBox);
            nameStatusLayout->addStretch();
            singleDoorLayout->addRow(nameStatusLayout);

            // 控制方式与开门延时同一行
            QHBoxLayout* controlDelayLayout = new QHBoxLayout();
            QComboBox* controlModeCombo = new QComboBox();
            controlModeCombo->addItem("在线");
            controlModeCombo->addItem("常开");
            controlModeCombo->addItem("常闭");
            controlModeCombo->setCurrentIndex(0); // 默认在线
            controlModeCombo->setFixedWidth(80);
            
            QSpinBox* openDelaySpinBox = new QSpinBox();
            openDelaySpinBox->setRange(0, 6000);
            openDelaySpinBox->setValue(3);
            openDelaySpinBox->setSuffix(" 秒");
            openDelaySpinBox->setFixedWidth(80);
            
            controlDelayLayout->addWidget(new QLabel("控制方式:"));
            controlDelayLayout->addWidget(controlModeCombo);
            controlDelayLayout->addWidget(new QLabel("开门延时:"));
            controlDelayLayout->addWidget(openDelaySpinBox);
            controlDelayLayout->addStretch();
            singleDoorLayout->addRow(controlDelayLayout);

            // 读卡器配置
            QHBoxLayout* readerLayout = new QHBoxLayout();
            
            // 进门读卡器
            QCheckBox* entryEnabledCheckBox = new QCheckBox("进门读卡器");
            entryEnabledCheckBox->setChecked(true);
            
            QCheckBox* entryAttendanceCheckBox = new QCheckBox("作考勤");
            
            // 出门读卡器
            QCheckBox* exitEnabledCheckBox = new QCheckBox("出门读卡器");
            exitEnabledCheckBox->setChecked(true);
            
            QCheckBox* exitAttendanceCheckBox = new QCheckBox("作考勤");
            
            readerLayout->addWidget(entryEnabledCheckBox);
            readerLayout->addWidget(entryAttendanceCheckBox);
            readerLayout->addWidget(exitEnabledCheckBox);
            readerLayout->addWidget(exitAttendanceCheckBox);
            readerLayout->addStretch();
            singleDoorLayout->addRow("读卡器:", readerLayout);

            doorLayout->addWidget(singleDoorGroup);
        }

        basicInfoLayout->addRow(doorGroup);

        // 按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        buttonLayout->addStretch();

        QPushButton* saveButton = new QPushButton("保存");
        saveButton->setMinimumWidth(80);
        buttonLayout->addWidget(saveButton);

        QPushButton* cancelButton = new QPushButton("取消");
        cancelButton->setMinimumWidth(80);
        buttonLayout->addWidget(cancelButton);

        // 添加到主布局
        mainLayout->addWidget(basicInfoTab, 1);
        mainLayout->addLayout(buttonLayout);

        // 连接信号
        connect(saveButton, &QPushButton::clicked, this, &QDialog::accept);
        connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
        
        // 连接网络模式切换
        connect(wanModeRadio, &QRadioButton::toggled, [wanConfigGroup](bool checked) {
            wanConfigGroup->setEnabled(checked);
        });
    }
};

#include "test_layout.moc"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    TestControllerDialog dialog;
    dialog.show();

    return app.exec();
}
