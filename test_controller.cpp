#include <QApplication>
#include <QDebug>
#include <QMessageBox>
#include "src/models/Controller.h"
#include "src/database/dao/ControllerDao.h"
#include "src/database/DatabaseFactory.h"
#include "src/config/DatabaseConfig.h"

using namespace AccessControl;

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // 初始化数据库
    DatabaseConfig config;
    auto dbProvider = DatabaseFactory::createProvider(config);
    
    if (!dbProvider) {
        QMessageBox::critical(nullptr, "错误", "无法创建数据库连接");
        return -1;
    }

    // 创建ControllerDao
    ControllerDao controllerDao(dbProvider);

    // 测试序列号验证
    qDebug() << "=== 测试序列号验证 ===";
    
    Controller testController;
    
    // 测试有效序列号
    testController.setSerialNumber("123456789");
    qDebug() << "序列号 123456789:" << testController.isValidSerialNumber() 
             << "类型:" << testController.getControllerType() 
             << "系列:" << testController.getSeriesName();
    
    testController.setSerialNumber("211234567");
    qDebug() << "序列号 211234567:" << testController.isValidSerialNumber() 
             << "类型:" << testController.getControllerType() 
             << "系列:" << testController.getSeriesName();
    
    testController.setSerialNumber("471234567");
    qDebug() << "序列号 471234567:" << testController.isValidSerialNumber() 
             << "类型:" << testController.getControllerType() 
             << "系列:" << testController.getSeriesName();
    
    // 测试无效序列号
    testController.setSerialNumber("123456");  // 长度不够
    qDebug() << "序列号 123456:" << testController.isValidSerialNumber();
    
    testController.setSerialNumber("523456789");  // 第一位无效
    qDebug() << "序列号 523456789:" << testController.isValidSerialNumber();
    
    testController.setSerialNumber("143456789");  // 第二位无效
    qDebug() << "序列号 143456789:" << testController.isValidSerialNumber();
    
    testController.setSerialNumber("172456789");  // 第二位是7但第三位无效
    qDebug() << "序列号 172456789:" << testController.isValidSerialNumber();

    // 测试创建控制器
    qDebug() << "\n=== 测试创建控制器 ===";
    
    Controller newController;
    newController.setControllerNumber(1);
    newController.setSerialNumber("211234567");
    newController.setEnabled(true);
    newController.setNetworkMode(Controller::NetworkMode::LAN);
    newController.setDescription("测试控制器");
    newController.setMobileRemoteEnabled(true);
    newController.setQrCodeEnabled(true);
    
    // 设置门配置
    QList<Controller::DoorConfig> doorConfigs;
    for (int i = 0; i < 2; ++i) {  // 双门控制器
        Controller::DoorConfig config;
        config.name = QString("测试门%1").arg(i + 1);
        config.enabled = true;
        config.controlMode = Controller::DoorControlMode::Online;
        config.openDelay = 3;
        config.entryReaderEnabled = true;
        config.exitReaderEnabled = true;
        config.entryAttendance = false;
        config.exitAttendance = false;
        doorConfigs.append(config);
    }
    newController.setAllDoorConfigs(doorConfigs);
    
    int controllerId = controllerDao.createController(newController);
    if (controllerId > 0) {
        qDebug() << "控制器创建成功，ID:" << controllerId;
        
        // 测试查找控制器
        Controller foundController = controllerDao.findById(controllerId);
        if (foundController.id() > 0) {
            qDebug() << "找到控制器:" << foundController.controllerNumber() 
                     << "序列号:" << foundController.serialNumber()
                     << "描述:" << foundController.description();
            
            // 测试门配置
            QList<Controller::DoorConfig> loadedConfigs = foundController.allDoorConfigs();
            qDebug() << "门配置数量:" << loadedConfigs.size();
            for (int i = 0; i < loadedConfigs.size(); ++i) {
                const Controller::DoorConfig& config = loadedConfigs[i];
                qDebug() << QString("门%1: %2, 启用:%3, 控制方式:%4")
                            .arg(i + 1)
                            .arg(config.name)
                            .arg(config.enabled)
                            .arg(static_cast<int>(config.controlMode));
            }
        } else {
            qDebug() << "查找控制器失败";
        }
    } else {
        qDebug() << "控制器创建失败";
    }

    // 测试获取下一个控制器编号
    int nextNumber = controllerDao.getNextControllerNumber();
    qDebug() << "下一个可用控制器编号:" << nextNumber;

    // 测试统计信息
    QMap<QString, int> stats = controllerDao.getStatistics();
    qDebug() << "控制器统计信息:";
    qDebug() << "总数:" << stats["total"];
    qDebug() << "启用:" << stats["enabled"];
    qDebug() << "禁用:" << stats["disabled"];

    QMessageBox::information(nullptr, "测试完成", "控制器功能测试完成，请查看控制台输出");
    
    return 0;
}
