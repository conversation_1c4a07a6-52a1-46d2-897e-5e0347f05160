@echo off
echo === 删除门禁控制系统数据库文件 ===
echo.

set "DB_PATH=%LOCALAPPDATA%\AccessControl\AccessControlSystem\access_control.db"

echo 数据库文件路径: %DB_PATH%
echo.

if exist "%DB_PATH%" (
    echo 发现数据库文件，正在删除...
    del "%DB_PATH%"
    if exist "%DB_PATH%" (
        echo ❌ 删除失败，请手动删除文件
    ) else (
        echo ✅ 数据库文件删除成功
    )
) else (
    echo ℹ️ 数据库文件不存在，无需删除
)

echo.
echo 删除完成！重新运行程序时会自动创建新的数据库文件。
echo.
pause 