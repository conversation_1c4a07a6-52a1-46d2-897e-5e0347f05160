/****************************************************************************
** Meta object code from reading C++ file 'ControllerDialog.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/views/ControllerDialog.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ControllerDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS_t {};
constexpr auto qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS = QtMocHelpers::stringData(
    "AccessControl::ControllerDialog",
    "onSerialNumberChanged",
    "",
    "onNetworkModeChanged",
    "onSaveClicked",
    "onCancelClicked",
    "onControllerNumberChanged",
    "onAreaChanged"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS_t {
    uint offsetsAndSizes[16];
    char stringdata0[32];
    char stringdata1[22];
    char stringdata2[1];
    char stringdata3[21];
    char stringdata4[14];
    char stringdata5[16];
    char stringdata6[26];
    char stringdata7[14];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS_t qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS = {
    {
        QT_MOC_LITERAL(0, 31),  // "AccessControl::ControllerDialog"
        QT_MOC_LITERAL(32, 21),  // "onSerialNumberChanged"
        QT_MOC_LITERAL(54, 0),  // ""
        QT_MOC_LITERAL(55, 20),  // "onNetworkModeChanged"
        QT_MOC_LITERAL(76, 13),  // "onSaveClicked"
        QT_MOC_LITERAL(90, 15),  // "onCancelClicked"
        QT_MOC_LITERAL(106, 25),  // "onControllerNumberChanged"
        QT_MOC_LITERAL(132, 13)   // "onAreaChanged"
    },
    "AccessControl::ControllerDialog",
    "onSerialNumberChanged",
    "",
    "onNetworkModeChanged",
    "onSaveClicked",
    "onCancelClicked",
    "onControllerNumberChanged",
    "onAreaChanged"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSAccessControlSCOPEControllerDialogENDCLASS[] = {

 // content:
      12,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,   50,    2, 0x08,    1 /* Private */,
       3,    0,   51,    2, 0x08,    2 /* Private */,
       4,    0,   52,    2, 0x08,    3 /* Private */,
       5,    0,   53,    2, 0x08,    4 /* Private */,
       6,    0,   54,    2, 0x08,    5 /* Private */,
       7,    0,   55,    2, 0x08,    6 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject AccessControl::ControllerDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSAccessControlSCOPEControllerDialogENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ControllerDialog, std::true_type>,
        // method 'onSerialNumberChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onNetworkModeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSaveClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onCancelClicked'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onControllerNumberChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAreaChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void AccessControl::ControllerDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ControllerDialog *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onSerialNumberChanged(); break;
        case 1: _t->onNetworkModeChanged(); break;
        case 2: _t->onSaveClicked(); break;
        case 3: _t->onCancelClicked(); break;
        case 4: _t->onControllerNumberChanged(); break;
        case 5: _t->onAreaChanged(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *AccessControl::ControllerDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AccessControl::ControllerDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSAccessControlSCOPEControllerDialogENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int AccessControl::ControllerDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    return _id;
}
QT_WARNING_POP
