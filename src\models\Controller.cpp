#include "Controller.h"
#include <QRegularExpression>
#include <QDebug>

namespace AccessControl {

Controller::Controller()
    : m_id(0)
    , m_controllerNumber(1)
    , m_serialNumber("")
    , m_enabled(true)
    , m_networkMode(NetworkMode::LAN)
    , m_ipAddress("")
    , m_port(60000)
    , m_areaId(0)
    , m_description("")
    , m_mobileRemoteEnabled(true)
    , m_qrCodeEnabled(true)
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
    // 初始化4个门的配置
    for (int i = 0; i < 4; ++i) {
        DoorConfig config;
        config.name = QString("门%1").arg(i + 1);
        config.enabled = true; // 默认启用所有门
        m_doorConfigs.append(config);
    }
}

Controller::Controller(int id, const QString& serialNumber)
    : Controller()
{
    m_id = id;
    m_serialNumber = serialNumber;
}

Controller::DoorConfig Controller::doorConfig(int doorIndex) const
{
    if (doorIndex >= 0 && doorIndex < m_doorConfigs.size()) {
        return m_doorConfigs[doorIndex];
    }
    return DoorConfig();
}

void Controller::setDoorConfig(int doorIndex, const DoorConfig& config)
{
    if (doorIndex >= 0 && doorIndex < m_doorConfigs.size()) {
        m_doorConfigs[doorIndex] = config;
    }
}

int Controller::getControllerType() const
{
    if (m_serialNumber.length() != 9) {
        return 0; // 无效
    }

    QChar firstChar = m_serialNumber[0];
    if (firstChar == '1') {
        return 1; // 单门控制器
    } else if (firstChar == '2') {
        return 2; // 双门控制器
    } else if (firstChar == '4') {
        return 4; // 四门控制器
    }

    return 0; // 无效
}

QString Controller::getSeriesName() const
{
    if (m_serialNumber.length() < 2) {
        return "未知系列";
    }

    QChar secondChar = m_serialNumber[1];
    switch (secondChar.toLatin1()) {
        case '1':
            return "ADCT系列";
        case '2':
            return "中性系列";
        case '3':
            return "Adroitor系列";
        case '5':
            return "WGACCESS系列";
        case '7':
            return "梯控/储物柜";
        default:
            return "未知系列";
    }
}

bool Controller::isValidSerialNumber() const
{
    // 检查长度
    if (m_serialNumber.length() != 9) {
        return false;
    }

    // 检查是否全为数字
    QRegularExpression digitRegex("^\\d{9}$");
    if (!digitRegex.match(m_serialNumber).hasMatch()) {
        return false;
    }

    // 检查第一位限制
    QChar firstChar = m_serialNumber[0];
    if (firstChar != '1' && firstChar != '2' && firstChar != '4') {
        return false;
    }

    // 检查第二位限制
    QChar secondChar = m_serialNumber[1];
    QList<QChar> validSecondChars = {'1', '2', '3', '5', '7'};
    if (!validSecondChars.contains(secondChar)) {
        return false;
    }

    // 检查第三位限制（当第二位是7时）
    if (secondChar == '7') {
        QChar thirdChar = m_serialNumber[2];
        QList<QChar> validThirdChars = {'1', '3', '5'};
        if (!validThirdChars.contains(thirdChar)) {
            return false;
        }
    }

    return true;
}

bool Controller::isBidirectionalController() const
{
    int type = getControllerType();
    return (type == 1 || type == 2); // 单门和双门控制器是双向的
}

bool Controller::isValidIpAddress() const
{
    if (m_networkMode == NetworkMode::LAN) {
        return true; // 局域网模式不需要IP
    }

    if (m_ipAddress.isEmpty()) {
        return false;
    }

    // 简单的IP地址格式验证
    QRegularExpression ipRegex("^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    return ipRegex.match(m_ipAddress).hasMatch();
}

} // namespace AccessControl
