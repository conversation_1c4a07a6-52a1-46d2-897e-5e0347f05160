#include <QCoreApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QDir>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 设置数据库路径
    QString dbPath = QDir::currentPath() + "/access_control.db";
    qDebug() << "Database path:" << dbPath;
    
    // 连接数据库
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(dbPath);
    
    if (!db.open()) {
        qDebug() << "Failed to open database:" << db.lastError().text();
        return 1;
    }
    
    qDebug() << "Database opened successfully";
    
    // 检查consumer_photos表结构
    QSqlQuery query(db);
    query.exec("PRAGMA table_info(consumer_photos)");
    
    qDebug() << "consumer_photos table structure:";
    while (query.next()) {
        QString name = query.value(1).toString();
        QString type = query.value(2).toString();
        bool notNull = query.value(3).toBool();
        QString defaultValue = query.value(4).toString();
        bool primaryKey = query.value(5).toBool();
        
        qDebug() << "  Column:" << name << "Type:" << type << "NotNull:" << notNull 
                 << "Default:" << defaultValue << "PrimaryKey:" << primaryKey;
    }
    
    // 检查是否有数据
    query.exec("SELECT COUNT(*) FROM consumer_photos");
    if (query.next()) {
        qDebug() << "consumer_photos table has" << query.value(0).toInt() << "records";
    }
    
    // 检查consumers表
    query.exec("SELECT COUNT(*) FROM consumers");
    if (query.next()) {
        qDebug() << "consumers table has" << query.value(0).toInt() << "records";
    }
    
    db.close();
    return 0;
}
