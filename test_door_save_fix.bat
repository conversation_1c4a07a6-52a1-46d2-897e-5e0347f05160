@echo off
echo 测试多门控制器保存修复
echo ========================

echo.
echo 1. 编译测试程序...
g++ -std=c++17 -I"C:/Qt/6.6.3/mingw_64/include" -I"C:/Qt/6.6.3/mingw_64/include/QtCore" -I"C:/Qt/6.6.3/mingw_64/include/QtSql" -L"C:/Qt/6.6.3/mingw_64/lib" -lQt6Core -lQt6Sql test_door_save.cpp -o test_door_save.exe

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！

echo.
echo 2. 运行测试程序...
test_door_save.exe

echo.
echo 3. 测试完成！
echo.
echo 请检查输出结果：
echo - 应该显示4个门配置都成功保存
echo - 验证结果应该显示4个门配置
echo.
echo 如果测试成功，说明多门控制器保存问题已修复。
echo 如果仍有问题，请检查数据库表结构和权限。

pause 