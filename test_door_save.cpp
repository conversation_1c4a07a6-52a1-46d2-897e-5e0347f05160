#include <QCoreApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 获取数据库路径
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QString dbPath = dataDir + "/access_control.db";
    qDebug() << "Database path:" << dbPath;
    
    // 连接数据库
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(dbPath);
    
    if (!db.open()) {
        qDebug() << "Failed to open database:" << db.lastError().text();
        return -1;
    }
    
    qDebug() << "Database opened successfully";
    
    // 测试门配置保存
    qDebug() << "\n=== Testing door config save ===";
    
    // 先获取一个控制器ID
    QSqlQuery query;
    if (query.exec("SELECT id FROM controllers LIMIT 1")) {
        if (query.next()) {
            int controllerId = query.value("id").toInt();
            qDebug() << "Testing with controller ID:" << controllerId;
            
            // 删除现有的门配置
            QSqlQuery deleteQuery;
            deleteQuery.prepare("DELETE FROM controller_doors WHERE controller_id = ?");
            deleteQuery.addBindValue(controllerId);
            if (deleteQuery.exec()) {
                qDebug() << "Deleted existing door configs for controller" << controllerId;
            }
            
            // 测试修复后的保存方法：为每个门配置创建新的查询对象
            bool allSuccess = true;
            for (int i = 0; i < 4; ++i) {
                // 为每个门配置创建新的查询对象，避免重复使用导致的绑定问题
                QSqlQuery insertQuery(db);
                insertQuery.prepare(
                    "INSERT INTO controller_doors ("
                    "controller_id, door_index, door_name, enabled, control_mode, open_delay, door_icon, "
                    "entry_reader_enabled, exit_reader_enabled, entry_reader_type, exit_reader_type, "
                    "entry_attendance, exit_attendance"
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
                );

                insertQuery.bindValue(0, controllerId);
                insertQuery.bindValue(1, i);
                insertQuery.bindValue(2, QString("测试门%1").arg(i + 1));
                insertQuery.bindValue(3, true);
                insertQuery.bindValue(4, 0);
                insertQuery.bindValue(5, 3);
                insertQuery.bindValue(6, 0);
                insertQuery.bindValue(7, true);
                insertQuery.bindValue(8, true);
                insertQuery.bindValue(9, "进门");
                insertQuery.bindValue(10, "出门");
                insertQuery.bindValue(11, false);
                insertQuery.bindValue(12, false);
                
                if (insertQuery.exec()) {
                    qDebug() << "Successfully inserted door config" << i << "for controller" << controllerId;
                } else {
                    qDebug() << "Failed to insert door config" << i << ":" << insertQuery.lastError().text();
                    allSuccess = false;
                }
            }
            
            qDebug() << "All door configs saved successfully:" << allSuccess;
            
            // 验证插入结果
            QSqlQuery verifyQuery;
            verifyQuery.prepare("SELECT door_index, door_name, enabled FROM controller_doors WHERE controller_id = ? ORDER BY door_index");
            verifyQuery.addBindValue(controllerId);
            if (verifyQuery.exec()) {
                qDebug() << "Verification - door configs for controller" << controllerId << ":";
                int count = 0;
                while (verifyQuery.next()) {
                    int doorIndex = verifyQuery.value("door_index").toInt();
                    QString doorName = verifyQuery.value("door_name").toString();
                    bool enabled = verifyQuery.value("enabled").toBool();
                    qDebug() << "  Door" << doorIndex << ":" << doorName << "Enabled:" << enabled;
                    count++;
                }
                qDebug() << "Total door configs found:" << count;
            }
        } else {
            qDebug() << "No controllers found in database";
        }
    } else {
        qDebug() << "Failed to query controllers:" << query.lastError().text();
    }
    
    db.close();
    return 0;
} 