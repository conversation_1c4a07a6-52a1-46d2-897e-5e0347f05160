{"artifacts": [{"path": "AccessControlSystem.exe"}, {"path": "AccessControlSystem.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.6.3/mingw_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 1, "file": 0, "line": 173, "parent": 0}, {"command": 2, "file": 0, "line": 134, "parent": 0}, {"command": 5, "file": 0, "line": 24, "parent": 0}, {"file": 3, "parent": 4}, {"command": 5, "file": 3, "line": 164, "parent": 5}, {"file": 2, "parent": 6}, {"command": 4, "file": 2, "line": 52, "parent": 7}, {"file": 1, "parent": 8}, {"command": 3, "file": 1, "line": 61, "parent": 9}, {"command": 5, "file": 3, "line": 164, "parent": 5}, {"file": 5, "parent": 11}, {"command": 4, "file": 5, "line": 52, "parent": 12}, {"file": 4, "parent": 13}, {"command": 3, "file": 4, "line": 61, "parent": 14}, {"command": 5, "file": 3, "line": 164, "parent": 5}, {"file": 7, "parent": 16}, {"command": 4, "file": 7, "line": 53, "parent": 17}, {"file": 6, "parent": 18}, {"command": 3, "file": 6, "line": 61, "parent": 19}, {"command": 4, "file": 7, "line": 41, "parent": 17}, {"file": 12, "parent": 21}, {"command": 7, "file": 12, "line": 39, "parent": 22}, {"command": 6, "file": 11, "line": 111, "parent": 23}, {"command": 5, "file": 10, "line": 76, "parent": 24}, {"file": 9, "parent": 25}, {"command": 4, "file": 9, "line": 52, "parent": 26}, {"file": 8, "parent": 27}, {"command": 3, "file": 8, "line": 61, "parent": 28}, {"command": 3, "file": 8, "line": 76, "parent": 28}, {"command": 4, "file": 5, "line": 40, "parent": 12}, {"file": 15, "parent": 31}, {"command": 7, "file": 15, "line": 39, "parent": 32}, {"command": 6, "file": 11, "line": 111, "parent": 33}, {"command": 5, "file": 10, "line": 76, "parent": 34}, {"file": 14, "parent": 35}, {"command": 4, "file": 14, "line": 52, "parent": 36}, {"file": 13, "parent": 37}, {"command": 3, "file": 13, "line": 61, "parent": 38}, {"command": 8, "file": 0, "line": 145, "parent": 0}, {"command": 9, "file": 0, "line": 156, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "defines": [{"backtrace": 3, "define": "MINGW_HAS_SECURE_API=1"}, {"backtrace": 3, "define": "QT_CORE_LIB"}, {"backtrace": 3, "define": "QT_GUI_LIB"}, {"backtrace": 3, "define": "QT_MULTIMEDIAWIDGETS_LIB"}, {"backtrace": 3, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 3, "define": "QT_NEEDS_QMAIN"}, {"backtrace": 3, "define": "QT_NETWORK_LIB"}, {"backtrace": 3, "define": "QT_SQL_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}, {"backtrace": 3, "define": "UNICODE"}, {"backtrace": 3, "define": "WIN32"}, {"backtrace": 3, "define": "WIN64"}, {"backtrace": 3, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 3, "define": "_UNICODE"}, {"backtrace": 3, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/include"}, {"backtrace": 40, "path": "C:/Users/<USER>/Documents/AccessControlSystem/src"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtCore"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/mkspecs/win32-g++"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtGui"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtSql"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtNetwork"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtMultimedia"}, {"backtrace": 3, "isSystem": true, "path": "C:/Qt/6.6.3/mingw_64/include/QtMultimediaWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [3, 3], "standard": "17"}, "sourceIndexes": [0, 1, 3, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 38, 40, 42, 44, 46, 48, 50, 52, 55, 57, 59, 61, 63, 65, 67, 69, 71, 75]}], "dependencies": [{"id": "AccessControlSystem_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "AccessControlSystem_autogen::@6890427a1f51a3e7e1df"}], "id": "AccessControlSystem::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/AccessControlSystem"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-mwindows", "role": "flags"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Sql.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6MultimediaWidgets.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Multimedia.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Network.a", "role": "libraries"}, {"backtrace": 10, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Widgets.a", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Gui.a", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6Core.a", "role": "libraries"}, {"backtrace": 20, "fragment": "-lmpr", "role": "libraries"}, {"backtrace": 20, "fragment": "-l<PERSON><PERSON>v", "role": "libraries"}, {"backtrace": 29, "fragment": "-lmingw32", "role": "libraries"}, {"backtrace": 29, "fragment": "C:\\Qt\\6.6.3\\mingw_64\\lib\\libQt6EntryPoint.a", "role": "libraries"}, {"backtrace": 30, "fragment": "-lshell32", "role": "libraries"}, {"backtrace": 39, "fragment": "-ld3d11", "role": "libraries"}, {"backtrace": 39, "fragment": "-ldxgi", "role": "libraries"}, {"backtrace": 39, "fragment": "-ldxguid", "role": "libraries"}, {"backtrace": 39, "fragment": "-ld3d12", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "AccessControlSystem", "nameOnDisk": "AccessControlSystem.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 38, 40, 42, 44, 46, 48, 50, 52, 55, 57, 59, 61, 63, 65, 67, 69, 71, 75]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 34, 35, 37, 39, 41, 43, 45, 47, 49, 51, 54, 56, 58, 60, 62, 64, 66, 68, 70, 73]}, {"name": "", "sourceIndexes": [53, 72, 74]}, {"name": "CMake Rules", "sourceIndexes": [76, 77]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/config/DatabaseConfig.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/config/DatabaseConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/IDatabaseProvider.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/database/DatabaseFactory.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/DatabaseFactory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/DatabaseMigration.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/DatabaseMigration.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/providers/SQLiteProvider.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/providers/SQLiteProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/Department.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/Department.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/Area.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/Area.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/Consumer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/Consumer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/Operator.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/Operator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/OperatorCard.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/OperatorCard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/OperatorBiometric.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/OperatorBiometric.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/models/Controller.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/models/Controller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/dao/DepartmentDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/dao/DepartmentDao.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/dao/AreaDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/dao/AreaDao.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/dao/ConsumerDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/dao/ConsumerDao.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/dao/OperatorDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/dao/OperatorDao.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/database/dao/OperatorCardDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/database/dao/OperatorBiometricDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "src/database/dao/ControllerDao.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/database/dao/ControllerDao.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/LoginWindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/LoginWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/AutoLoginDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/AutoLoginDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/DepartmentManagementWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/DepartmentManagementWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/AreaManagementWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/AreaManagementWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerManagementWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerManagementWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/GlobalSearchDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/GlobalSearchDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/OperatorManagementWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/OperatorManagementWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/OperatorManagementWidget.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "src/views/OperatorDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/OperatorDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ControllerDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ControllerDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerCardDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerCardDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerPhotoWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerPhotoWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/CardLineEdit.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/CardLineEdit.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerFingerprintWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerFingerprintWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/ConsumerCardListWidget.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/ConsumerCardListWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "src/views/CameraDialog.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/views/CameraDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 41, "path": "resources.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/include/src/views/ui_OperatorManagementWidget.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/EWIEGA46WW/qrc_resources.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/timestamp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/AccessControlSystem_autogen/EWIEGA46WW/qrc_resources.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}