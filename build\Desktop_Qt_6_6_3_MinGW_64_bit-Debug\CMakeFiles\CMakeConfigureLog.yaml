
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-fdx5zn"
      binary: "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-fdx5zn"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-fdx5zn'
        
        Run Build Command(s): C:/ninja/ninja.exe -v cmTC_03bbf
        [1/2] C:\\Qt\\Tools\\mingw1120_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_03bbf.dir\\CMakeCXXCompilerABI.cpp.obj -c C:\\Qt\\Tools\\CMake_64\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        FAILED: [code=1] CMakeFiles/cmTC_03bbf.dir/CMakeCXXCompilerABI.cpp.obj 
        C:\\Qt\\Tools\\mingw1120_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_03bbf.dir\\CMakeCXXCompilerABI.cpp.obj -c C:\\Qt\\Tools\\CMake_64\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working CXX compiler: C:/Qt/Tools/mingw1120_64/bin/g++.exe"
    directories:
      source: "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-16195s"
      binary: "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-16195s"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/CMakeFiles/CMakeScratch/TryCompile-16195s'
        
        Run Build Command(s): C:/ninja/ninja.exe -v cmTC_0fc3d
        [1/2] C:\\Qt\\Tools\\mingw1120_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_0fc3d.dir\\testCXXCompiler.cxx.obj -c C:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-16195s\\testCXXCompiler.cxx
        FAILED: [code=1] CMakeFiles/cmTC_0fc3d.dir/testCXXCompiler.cxx.obj 
        C:\\Qt\\Tools\\mingw1120_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_0fc3d.dir\\testCXXCompiler.cxx.obj -c C:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-16195s\\testCXXCompiler.cxx
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
