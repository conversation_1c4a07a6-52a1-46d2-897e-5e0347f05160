<!DOCTYPE RCC><RCC version="1.0">
<qresource>
    <!-- 数据库迁移文件 - resources目录 -->
    <file>resources/sql/migrations/001_create_users_table.sql</file>
    <file>resources/sql/migrations/004_create_user_profiles_table.sql</file>
    <file>resources/sql/migrations/004_extend_users_table.sql</file>
    <file>resources/sql/migrations/005_1_add_missing_fields_to_users.sql</file>
    <file>resources/sql/migrations/005_create_controllers_table.sql</file>
    <file>resources/sql/migrations/006_create_controller_doors_table.sql</file>
    <file>resources/sql/migrations/006_create_doors_table.sql</file>
    <file>resources/sql/migrations/007_create_access_records_table.sql</file>
    <file>resources/sql/migrations/007_update_controllers_table.sql</file>
    <file>resources/sql/migrations/008_add_door_icon_field.sql</file>
    <file>resources/sql/migrations/008_create_consumers_table.sql</file>
    <file>resources/sql/migrations/009_add_reader_type_fields.sql</file>
    <file>resources/sql/migrations/009_migrate_user_data.sql</file>
    <file>resources/sql/migrations/010_create_operators_table.sql</file>
    <file>resources/sql/migrations/011_extend_consumers_table.sql</file>
    <file>resources/sql/migrations/012_create_departments_table.sql</file>
    <file>resources/sql/migrations/013_create_areas_table.sql</file>
    <file>resources/sql/migrations/014_fix_controllers_table.sql</file>

    <!-- 数据库迁移文件 - src目录 -->
    <file>src/database/migrations/004_create_user_profiles_table.sql</file>
    <file>src/database/migrations/004_extend_users_table.sql</file>
    <file>src/database/migrations/005_add_missing_fields_to_users.sql</file>
    <file>src/database/migrations/008_create_consumers_table.sql</file>
    <file>src/database/migrations/009_migrate_user_data.sql</file>
    <file>src/database/migrations/011_extend_consumers_table.sql</file>
    <file>src/database/migrations/2024_areas.sql</file>

    <!-- 图片资源 -->
    <file>resources/images/lock.png</file>

    <!-- 模板文件 -->
    <file>resources/area_template.csv</file>
    <file>resources/department_template.csv</file>
</qresource>
</RCC>