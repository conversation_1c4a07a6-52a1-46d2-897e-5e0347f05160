[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\main.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\config\\DatabaseConfig.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/config/DatabaseConfig.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\DatabaseFactory.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/DatabaseFactory.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\DatabaseMigration.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/DatabaseMigration.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\providers\\SQLiteProvider.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/providers/SQLiteProvider.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Department.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Department.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Area.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Area.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Consumer.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Consumer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Operator.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Operator.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\OperatorCard.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/OperatorCard.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\OperatorBiometric.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/OperatorBiometric.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Controller.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Controller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\DepartmentDao.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/DepartmentDao.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\AreaDao.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/AreaDao.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\ConsumerDao.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/ConsumerDao.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\OperatorDao.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/OperatorDao.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\ControllerDao.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/ControllerDao.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\LoginWindow.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/LoginWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\MainWindow.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/MainWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\AutoLoginDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/AutoLoginDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\DepartmentManagementWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/DepartmentManagementWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\AreaManagementWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/AreaManagementWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerManagementWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerManagementWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\GlobalSearchDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/GlobalSearchDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\OperatorManagementWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/OperatorManagementWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\OperatorDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/OperatorDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ControllerDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ControllerDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerCardDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerCardDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerPhotoWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerPhotoWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\CardLineEdit.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/CardLineEdit.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerFingerprintWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerFingerprintWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerCardListWidget.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerCardListWidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\CameraDialog.cpp"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/CameraDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\config\\DatabaseConfig.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/config/DatabaseConfig.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\IDatabaseProvider.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/IDatabaseProvider.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\DatabaseFactory.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/DatabaseFactory.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\DatabaseMigration.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/DatabaseMigration.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\providers\\SQLiteProvider.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/providers/SQLiteProvider.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Department.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Department.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Area.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Area.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Consumer.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Consumer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Operator.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Operator.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\OperatorCard.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/OperatorCard.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\OperatorBiometric.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/OperatorBiometric.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\models\\Controller.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/models/Controller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\DepartmentDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/DepartmentDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\AreaDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/AreaDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\ConsumerDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/ConsumerDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\OperatorDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/OperatorDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\OperatorCardDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/OperatorCardDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\OperatorBiometricDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/OperatorBiometricDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\database\\dao\\ControllerDao.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/database/dao/ControllerDao.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\LoginWindow.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/LoginWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\MainWindow.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/MainWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\AutoLoginDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/AutoLoginDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\DepartmentManagementWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/DepartmentManagementWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\AreaManagementWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/AreaManagementWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerManagementWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerManagementWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\GlobalSearchDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/GlobalSearchDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\OperatorManagementWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/OperatorManagementWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\OperatorDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/OperatorDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ControllerDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ControllerDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerCardDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerCardDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerPhotoWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerPhotoWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\CardLineEdit.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/CardLineEdit.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerFingerprintWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerFingerprintWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\ConsumerCardListWidget.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/ConsumerCardListWidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIAWIDGETS_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_SQL_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\build\\Desktop_Qt_6_6_3_MinGW_64_bit-Debug\\AccessControlSystem_autogen\\include", "-IC:\\Users\\<USER>\\Documents\\AccessControlSystem\\src", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtSql", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\6.6.3\\mingw_64\\include\\QtMultimediaWidgets", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\lib\\gcc\\x86_64-w64-mingw32\\11.2.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1120_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Documents\\AccessControlSystem\\src\\views\\CameraDialog.h"], "directory": "C:/Users/<USER>/Documents/AccessControlSystem/build/Desktop_Qt_6_6_3_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Documents/AccessControlSystem/src/views/CameraDialog.h"}]