#ifndef CONTROLLERDAO_H
#define CONTROLLERDAO_H

#include "../../models/Controller.h"
#include "../IDatabaseProvider.h"
#include <QList>
#include <memory>

namespace AccessControl {

/**
 * @brief 控制器数据访问对象
 * 提供控制器的CRUD操作和业务逻辑
 */
class ControllerDao {
public:
    using DatabaseProviderPtr = std::shared_ptr<IDatabaseProvider>;

    explicit ControllerDao(DatabaseProviderPtr provider);
    ~ControllerDao();

    // ========== 基本CRUD操作 ==========
    /**
     * @brief 创建控制器
     * @param controller 控制器对象，成功后会设置ID
     * @return 成功返回控制器ID，失败返回-1
     */
    int createController(Controller& controller);

    /**
     * @brief 更新控制器
     * @param controller 控制器对象
     * @return 成功返回true，失败返回false
     */
    bool updateController(const Controller& controller);

    /**
     * @brief 删除控制器
     * @param controllerId 控制器ID
     * @return 成功返回true，失败返回false
     */
    bool deleteController(int controllerId);

    /**
     * @brief 根据ID查找控制器
     * @param controllerId 控制器ID
     * @return 控制器对象，未找到返回空对象
     */
    Controller findById(int controllerId);

    /**
     * @brief 根据序列号查找控制器
     * @param serialNumber 序列号
     * @return 控制器对象，未找到返回空对象
     */
    Controller findBySerialNumber(const QString& serialNumber);

    /**
     * @brief 根据控制器编号查找控制器
     * @param controllerNumber 控制器编号
     * @return 控制器对象，未找到返回空对象
     */
    Controller findByControllerNumber(int controllerNumber);

    // ========== 查询操作 ==========
    /**
     * @brief 获取所有控制器
     * @return 控制器列表
     */
    QList<Controller> findAll();

    /**
     * @brief 获取启用的控制器
     * @return 启用的控制器列表
     */
    QList<Controller> findEnabled();

    /**
     * @brief 根据区域ID查找控制器
     * @param areaId 区域ID
     * @return 控制器列表
     */
    QList<Controller> findByAreaId(int areaId);

    /**
     * @brief 根据IP地址查找控制器
     * @param ipAddress IP地址
     * @return 控制器列表
     */
    QList<Controller> findByIpAddress(const QString& ipAddress);

    // ========== 业务逻辑方法 ==========
    /**
     * @brief 检查序列号是否已存在
     * @param serialNumber 序列号
     * @param excludeId 排除的控制器ID（用于更新时检查）
     * @return 存在返回true，不存在返回false
     */
    bool isSerialNumberExists(const QString& serialNumber, int excludeId = -1);

    /**
     * @brief 检查控制器编号是否已存在
     * @param controllerNumber 控制器编号
     * @param excludeId 排除的控制器ID（用于更新时检查）
     * @return 存在返回true，不存在返回false
     */
    bool isControllerNumberExists(int controllerNumber, int excludeId = -1);

    /**
     * @brief 获取下一个可用的控制器编号
     * @return 下一个可用的控制器编号
     */
    int getNextControllerNumber();

    /**
     * @brief 验证控制器数据
     * @param controller 控制器对象
     * @param isUpdate 是否为更新操作
     * @return 验证结果，first为是否通过，second为错误信息
     */
    QPair<bool, QString> validateController(const Controller& controller, bool isUpdate = false);

    /**
     * @brief 获取控制器统计信息
     * @return 统计信息映射表
     */
    QMap<QString, int> getStatistics();

private:
    DatabaseProviderPtr m_provider;

    /**
     * @brief 从查询结果构建控制器对象
     * @param query SQL查询对象
     * @return 控制器对象
     */
    Controller buildControllerFromQuery(const QSqlQuery& query);

    /**
     * @brief 将控制器对象绑定到查询
     * @param query SQL查询对象
     * @param controller 控制器对象
     * @param includeId 是否包含ID字段
     */
    void bindControllerToQuery(QSqlQuery& query, const Controller& controller, bool includeId = false);

    /**
     * @brief 保存门配置到数据库
     * @param controllerId 控制器ID
     * @param doorConfigs 门配置列表
     * @return 成功返回true，失败返回false
     */
    bool saveDoorConfigs(int controllerId, const QList<Controller::DoorConfig>& doorConfigs);

    /**
     * @brief 从数据库加载门配置
     * @param controllerId 控制器ID
     * @return 门配置列表
     */
    QList<Controller::DoorConfig> loadDoorConfigs(int controllerId);

    /**
     * @brief 删除门配置
     * @param controllerId 控制器ID
     * @return 成功返回true，失败返回false
     */
    bool deleteDoorConfigs(int controllerId);
};

} // namespace AccessControl

#endif // CONTROLLERDAO_H
