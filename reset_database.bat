@echo off
echo 重置数据库 - 彻底解决架构问题
echo ================================

echo.
echo 1. 停止应用程序（如果正在运行）...
taskkill /f /im AccessControlSystem.exe 2>nul

echo.
echo 2. 删除现有数据库文件...
set DB_PATH="C:\Users\<USER>\AppData\Local\AccessControlSystem\access_control.db"
if exist %DB_PATH% (
    del /f %DB_PATH%
    echo 已删除数据库文件: %DB_PATH%
) else (
    echo 数据库文件不存在，无需删除
)

echo.
echo 3. 重新编译项目...
cmake -B build_new -S .
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

cmake --build build_new --config Debug
if %ERRORLEVEL% NEQ 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 4. 运行应用程序创建新数据库...
echo 请手动运行应用程序，系统将自动创建新的数据库架构
echo.
echo 5. 验证修复效果：
echo    - 区域应该显示完整的层级关系（使用"\"分隔符）
echo    - 多门控制器应该能够保存所有门的配置
echo    - 控制器列表中应该显示所有启用的门名称
echo.
echo 如果问题仍然存在，请检查日志输出并报告具体错误信息。

pause 