#ifndef OPERATORCARDDAO_H
#define OPERATORCARDDAO_H

#include "../IDatabaseProvider.h"
#include "../../models/OperatorCard.h"
#include <QList>
#include <QVariant>
#include <memory>

namespace AccessControl {

/**
 * @brief 操作员卡片数据访问对象
 * 负责操作员卡片信息的数据库操作
 */
class OperatorCardDao {
public:
    explicit OperatorCardDao(std::shared_ptr<IDatabaseProvider> provider);
    ~OperatorCardDao() = default;

    // ========== 表管理 ==========

    /**
     * @brief 创建操作员卡片表
     * @return 创建是否成功
     */
    bool createTable();

    /**
     * @brief 检查操作员卡片表是否存在
     * @return 表是否存在
     */
    bool tableExists();

    /**
     * @brief 初始化表结构
     * @return 初始化是否成功
     */
    bool initializeTable();

    // ========== CRUD 操作 ==========

    /**
     * @brief 创建操作员卡片
     * @param card 操作员卡片对象
     * @return 创建是否成功，成功时会设置ID
     */
    bool createOperatorCard(OperatorCard& card);

    /**
     * @brief 根据ID获取操作员卡片
     * @param cardId 卡片ID
     * @return 操作员卡片对象
     */
    OperatorCard getOperatorCardById(int cardId);

    /**
     * @brief 根据操作员ID获取所有卡片
     * @param operatorId 操作员ID
     * @return 操作员卡片列表
     */
    QList<OperatorCard> getOperatorCardsByOperatorId(int operatorId);

    /**
     * @brief 根据卡号获取操作员卡片
     * @param cardNumber 卡号
     * @return 操作员卡片对象
     */
    OperatorCard getOperatorCardByNumber(const QString& cardNumber);

    /**
     * @brief 更新操作员卡片
     * @param card 操作员卡片对象
     * @return 更新是否成功
     */
    bool updateOperatorCard(const OperatorCard& card);

    /**
     * @brief 删除操作员卡片
     * @param cardId 卡片ID
     * @return 删除是否成功
     */
    bool deleteOperatorCard(int cardId);

    /**
     * @brief 根据操作员ID删除所有卡片
     * @param operatorId 操作员ID
     * @return 删除是否成功
     */
    bool deleteOperatorCardsByOperatorId(int operatorId);

    /**
     * @brief 获取所有操作员卡片
     * @return 操作员卡片列表
     */
    QList<OperatorCard> getAllOperatorCards();

    // ========== 查询和搜索 ==========

    /**
     * @brief 根据卡片类型获取操作员卡片
     * @param cardType 卡片类型
     * @return 操作员卡片列表
     */
    QList<OperatorCard> getOperatorCardsByType(OperatorCard::CardType cardType);

    /**
     * @brief 根据卡片状态获取操作员卡片
     * @param status 卡片状态
     * @return 操作员卡片列表
     */
    QList<OperatorCard> getOperatorCardsByStatus(OperatorCard::CardStatus status);

    /**
     * @brief 获取操作员的主卡
     * @param operatorId 操作员ID
     * @return 主卡对象
     */
    OperatorCard getOperatorPrimaryCard(int operatorId);

    /**
     * @brief 搜索操作员卡片
     * @param keyword 关键词（卡号、备注）
     * @param cardType 卡片类型过滤
     * @param status 卡片状态过滤
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 操作员卡片列表
     */
    QList<OperatorCard> searchOperatorCards(const QString& keyword = QString(),
                                           const QString& cardType = QString(),
                                           const QString& status = QString(),
                                           int limit = 50,
                                           int offset = 0);

    // ========== 卡片管理 ==========

    /**
     * @brief 设置主卡
     * @param cardId 卡片ID
     * @return 设置是否成功
     */
    bool setPrimaryCard(int cardId);

    /**
     * @brief 取消主卡
     * @param operatorId 操作员ID
     * @return 取消是否成功
     */
    bool unsetPrimaryCard(int operatorId);

    /**
     * @brief 更新卡片最后使用时间
     * @param cardId 卡片ID
     * @param lastUsed 最后使用时间
     * @return 更新是否成功
     */
    bool updateLastUsed(int cardId, const QDateTime& lastUsed);

    /**
     * @brief 检查卡号是否存在
     * @param cardNumber 卡号
     * @param excludeCardId 排除的卡片ID
     * @return 卡号是否存在
     */
    bool cardNumberExists(const QString& cardNumber, int excludeCardId = 0);

    // ========== 统计信息 ==========

    /**
     * @brief 获取操作员卡片总数
     * @return 总数
     */
    int getOperatorCardCount();

    /**
     * @brief 获取操作员卡片统计
     * @param operatorId 操作员ID
     * @return 统计信息映射
     */
    QMap<QString, int> getOperatorCardStats(int operatorId);

    /**
     * @brief 获取卡片类型分布
     * @return 类型分布映射
     */
    QMap<QString, int> getCardTypeDistribution();

    // ========== 批量操作 ==========

    /**
     * @brief 批量创建操作员卡片
     * @param cards 操作员卡片列表
     * @return 成功创建的数量
     */
    int batchCreateOperatorCards(const QList<OperatorCard>& cards);

    /**
     * @brief 批量更新操作员卡片
     * @param cards 操作员卡片列表
     * @return 成功更新的数量
     */
    int batchUpdateOperatorCards(const QList<OperatorCard>& cards);

private:
    std::shared_ptr<IDatabaseProvider> m_provider;

    /**
     * @brief 从查询结果构建操作员卡片对象
     * @param query 查询结果
     * @return 操作员卡片对象
     */
    OperatorCard buildOperatorCardFromQuery(const QSqlQuery& query);

    /**
     * @brief 获取操作员卡片表的SQL创建语句
     * @return SQL语句
     */
    QString getOperatorCardTableSQL();

    /**
     * @brief 获取插入操作员卡片的SQL语句
     * @return SQL语句
     */
    QString getInsertOperatorCardSQL();

    /**
     * @brief 获取更新操作员卡片的SQL语句
     * @return SQL语句
     */
    QString getUpdateOperatorCardSQL();

    /**
     * @brief 检查数据库提供者是否有效
     * @return 是否有效
     */
    bool isProviderValid();
};

} // namespace AccessControl

#endif // OPERATORCARDDAO_H