-- 为控制器门配置表添加读卡器性质字段

ALTER TABLE controller_doors ADD COLUMN entry_reader_type VARCHAR(50) NOT NULL DEFAULT '进门';
ALTER TABLE controller_doors ADD COLUMN exit_reader_type VARCHAR(50) NOT NULL DEFAULT '出门';

-- 更新现有记录的读卡器性质为默认值
UPDATE controller_doors SET entry_reader_type = '进门' WHERE entry_reader_type IS NULL OR entry_reader_type = '';
UPDATE controller_doors SET exit_reader_type = '出门' WHERE exit_reader_type IS NULL OR exit_reader_type = '';
