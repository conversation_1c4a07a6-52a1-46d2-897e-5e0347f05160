# 门禁卡持有者管理功能开发计划

**时间**: 2024-06-09
**更新**: 2025-01-30 (重构：用户分离为操作员和门禁卡持有者)

## 1. 项目目标

开发完善的门禁卡持有者管理功能，包括添加、编辑、删除门禁卡持有者，以及管理门禁卡持有者的扩展信息、卡片信息和生物识别数据。

**注意**: 原用户概念已分离为：
- **系统操作员(Operator)**: 系统登录用户，负责系统操作
- **门禁卡持有者(Consumer)**: 门禁卡的持有者，不能登录系统

## 2. 功能模块清单

### 2.1 添加门禁卡持有者对话框
- 非模态对话框设计
- 主要信息与扩展信息的标签页分组
- 照片上传与拍照功能
- 指纹登记功能
- 多卡管理功能

### 2.2 门禁卡持有者主要信息管理
- 工号
- 姓名
- 卡号（支持多卡）
- 考勤启用状态
- 倒班状态
- 门禁启用状态
- 起始日期（默认为当前日期）
- 截止日期（默认为2099-12-31）
- 部门选择
- 手机号
- 身份证号

### 2.3 门禁卡持有者扩展信息管理
- 性别
- 民族
- 宗教
- 籍贯
- 出生年月
- 婚姻状态
- 政治面貌
- 学历
- 工作电话
- 家庭电话
- 英文名
- 单位
- 职称
- 技术等级
- 证件名称
- 证件号
- 社保号
- 入职时间
- 离职时间
- 电子邮箱
- 通讯地址
- 邮编
- 备注

### 2.4 门禁卡持有者卡片管理
- 支持多种卡类型：IC/ID卡、CPU卡、指纹、人脸、手机号、身份证、胁迫卡、母卡
- 卡片启用/禁用状态
- 设置主卡
- 生物识别卡绑定到物理卡

### 2.5 门禁卡持有者生物识别数据管理
- 照片上传/拍照
- 指纹录入
- 人脸录入

## 3. 技术方案

### 3.1 数据库设计
现有数据库表已基本满足需求，包括：
- consumers: 门禁卡持有者基本信息
- consumer_profiles: 门禁卡持有者扩展信息
- consumer_cards: 门禁卡持有者卡片信息
- consumer_photos: 门禁卡持有者照片数据
- operators: 系统操作员基本信息（用于系统登录）

### 3.2 类设计
#### 3.2.1 UI 类
- `ConsumerDialog`: 门禁卡持有者添加/编辑对话框
  - 继承自 `QDialog`
  - 非模态对话框
  - 包含标签页分组

- `ConsumerPhotoWidget`: 门禁卡持有者照片管理组件
  - 继承自 `QWidget`
  - 支持上传和拍照

- `ConsumerFingerprintWidget`: 门禁卡持有者指纹管理组件
  - 继承自 `QWidget`
  - 支持指纹录入

- `ConsumerCardListWidget`: 门禁卡持有者卡片列表组件
  - 继承自 `QWidget`
  - 显示门禁卡持有者的所有卡片
  - 支持添加/删除卡片

#### 3.2.2 模型类
使用现有的模型类：
- `User`: 用户模型
- `UserCard`: 用户卡片模型
- `UserBiometric`: 用户生物识别模型

### 3.3 交互设计
- 对话框采用非模态设计，不阻塞主窗口操作
- 标签页分组简化界面，减少视觉复杂度
- 表单验证即时反馈
- 生物识别数据采集提供视觉反馈
- 支持多卡管理的直观界面

## 4. 任务分解

### 4.1 基础框架搭建
- [x] 分析现有代码结构
- [x] 创建 `UserDialog` 类
- [x] 设计对话框布局
- [x] 实现标签页分组

### 4.2 主要信息标签页
- [x] 设计表单布局
- [x] 实现表单字段绑定
- [x] 实现部门选择组件
- [x] 实现表单验证

### 4.3 扩展信息标签页
- [x] 设计表单布局
- [x] 实现表单字段绑定
- [x] 实现表单验证

### 4.4 用户照片管理
- [x] 实现照片上传功能
- [x] 实现拍照功能
- [x] 实现照片预览和裁剪

### 4.5 指纹管理
- [x] 设计指纹录入界面
- [x] 实现指纹录入功能
- [x] 实现指纹验证功能

### 4.6 卡片管理
- [x] 设计卡片列表界面
- [x] 实现添加/删除卡片功能
- [x] 实现设置主卡功能
- [x] 实现生物识别卡绑定到物理卡功能

### 4.7 数据保存
- [x] 实现用户主要信息保存
- [x] 实现用户扩展信息保存
- [x] 实现用户卡片信息保存
- [x] 实现用户生物识别数据保存

### 4.8 测试与调试
- [x] 表单验证测试
- [x] 数据保存测试
- [x] 照片和指纹功能测试
- [x] 卡片管理功能测试

## 5. 时间节点

- 第1天: 基础框架搭建，主要信息标签页 ✓
- 第2天: 扩展信息标签页，照片管理 ✓
- 第3天: 指纹管理，卡片管理 ✓
- 第4天: 数据保存，测试与调试 ✓
- 第5天: 完善功能，修复问题 ✓

## 6. 风险评估

### 6.1 技术风险
- 指纹设备集成可能存在兼容性问题
- 照片采集可能需要适配不同的摄像头设备
- 多卡管理逻辑复杂，需要仔细设计

### 6.2 项目风险
- 功能需求可能随开发过程调整
- 界面设计可能需要多次迭代
- 生物识别数据的安全存储需要特别关注

## 7. 设计决策

### 7.1 用户卡片与生物识别关系
- 每个用户可以有多张卡片（物理卡和虚拟卡）
- 生物识别数据（指纹、人脸）作为一种特殊的卡片类型
- 可以将生物识别卡绑定到物理卡，实现多因素认证

### 7.2 数据存储策略
- 用户主要信息存储在 users 表
- 用户扩展信息存储在 user_profiles 表
- 卡片信息存储在 user_cards 表
- 生物识别数据存储在 user_biometric 表
- 照片存储为文件，路径保存在数据库中

### 7.3 UI设计策略
- 采用简洁的表单设计
- 常用字段放在主要信息标签页
- 不常用字段放在扩展信息标签页
- 照片和指纹操作放在独立区域

## 8. 完成情况

**完成日期**: 2024-06-09

### 8.1 已完成功能
- 用户添加/编辑对话框设计与实现
- 主要信息与扩展信息的标签页分组
- 照片上传与拍照功能
- 指纹录入功能
- 多卡管理功能
- 与ConsumerManagementWidget的集成

### 8.2 后续改进
- 完善数据库操作，实现真实数据的保存和读取
- 优化用户界面，提升用户体验
- 增强表单验证，提高数据质量
- 实现真实的指纹设备集成
- 增加批量导入导出功能

---

## 9. 重大更新 - 2025年7月26日

### 9.1 架构重构
**用户类型分离重构**
- **问题**: 原系统Operator和Consumer概念混淆
- **解决**: 完全分离两种用户类型，各自独立管理
- **影响**: 提升系统架构清晰度，减少开发和维护混淆

**数据库架构升级**
- 新增`operators`表：系统登录用户管理
- 扩展`consumers`相关表：门禁用户完整信息
- 实现完整的数据库迁移机制

### 9.2 核心功能完善

**部门管理系统**
- [x] 修复departments表缺失问题
- [x] 实现递归CTE路径计算算法
- [x] 支持无限层级部门结构
- [x] 自动维护"/"分隔的完整路径
- [x] 真实数据库集成，移除硬编码

**区域管理系统**
- [x] 参照部门方案修复areas表
- [x] 完整的索引和数据结构
- [x] 与AreaDao完全匹配的字段设计

**用户对话框增强**
- [x] 智能照片命名系统
  - 优先级：人脸卡号 > 主卡号 > 工号
  - 动态更新机制
- [x] 卡片必填验证
  - UI红色标识提醒
  - 表单验证确保数据完整性
- [x] 卡片编辑修复
  - 显示当前卡号、类型、主卡状态
  - 完整的setter方法实现
- [x] USB读卡器支持
  - 自定义CardLineEdit类
  - 智能处理换行符和追加问题

**拍照功能重构**
- [x] 相机稳定性大幅提升
- [x] 证件照比例裁剪（3:4）
- [x] 预览和拍照防拉伸
- [x] 完善的错误处理机制
- [x] 照片上传自动裁剪

### 9.3 技术亮点

**递归CTE算法**
```sql
WITH RECURSIVE department_hierarchy AS (
    SELECT id, name, parent_id, 1 as level, name as full_path
    FROM departments WHERE parent_id IS NULL
    UNION ALL
    SELECT d.id, d.name, d.parent_id,
           dh.level + 1, dh.full_path || '/' || d.name
    FROM departments d
    JOIN department_hierarchy dh ON d.parent_id = dh.id
)
```

**智能照片命名**
- 业务驱动的命名策略
- 动态更新机制
- 与卡片管理联动

**自定义输入控件**
- 解决硬件设备兼容性
- 优雅的事件处理
- 用户体验提升

### 9.4 质量保证

**测试覆盖**
- [x] 部门创建和多级显示测试
- [x] 区域管理基础功能测试
- [x] 用户对话框集成测试
- [x] 卡片编辑功能验证
- [x] 照片命名逻辑测试
- [x] USB读卡器输入测试
- [x] 相机拍照功能测试

**代码质量**
- 完善的错误处理机制
- 详细的调试日志
- 规范的代码结构
- 单一职责原则

### 9.5 文档更新
- [x] 详细的开发日志（dev-log-2025-07-26.md）
- [x] 更新项目README
- [x] 完善开发计划文档
- [x] 技术决策记录

### 9.6 后续规划

**短期目标（1-2周）**
- 完善ConsumerDao真实数据保存
- 实现批量用户导入导出
- 优化大数据量性能
- 增加操作进度反馈

**中期目标（1个月）**
- 权限管理系统设计与实现
- 考勤管理功能开发
- 报表系统基础框架
- 审计日志记录

**长期目标（3个月）**
- 完整的权限角色体系
- 高级报表和统计功能
- 系统性能优化
- 移动端支持

### 9.7 技术债务管理

**已解决**
- ✅ 用户类型概念混淆
- ✅ 数据库表结构缺失
- ✅ 硬编码数据问题
- ✅ 多级路径显示问题
- ✅ 相机功能稳定性

**待处理**
- 📋 增加单元测试覆盖
- 📋 API文档完善
- 📋 国际化支持
- 📋 性能监控机制
- 📋 用户操作手册

### 9.8 经验总结

**成功经验**
1. **渐进式重构**: 保持系统可用性的同时逐步改进
2. **用户反馈驱动**: 及时响应用户需求，快速迭代
3. **技术选型谨慎**: 充分调研后选择最适合的技术方案
4. **文档先行**: 详细记录设计决策和实现细节

**改进方向**
1. **测试驱动开发**: 增加测试用例，提前发现问题
2. **性能监控**: 建立性能基线，持续优化
3. **代码审查**: 建立代码质量检查机制
4. **知识沉淀**: 建立技术知识库，便于团队学习

---

## 10. 最新进展 (2025-07-31)

### 10.1 关键问题修复
- ✅ **区域管理保存修复**: 解决了区域信息保存失败的问题
- ✅ **控制器管理优化**: 修复了控制器保存失败和列表刷新问题
- ✅ **门配置优化**: 改进了门配置默认设置，所有门默认启用
- ✅ **数据库架构完善**: 修复了表结构不匹配和参数绑定错误

### 10.2 功能状态更新
- **区域管理**: 完全可用，支持层级显示和数据保存
- **控制器管理**: 完全可用，支持自动刷新和门配置
- **用户管理**: 功能完善，包括所有扩展功能
- **部门管理**: 功能完善，支持多级结构

### 10.3 技术债务清理
- 解决了多个编译错误和依赖问题
- 统一了数据库操作的错误处理
- 完善了调试日志和错误诊断机制

---

**最新更新日期**: 2025年7月31日
**项目状态**: 核心模块稳定，主要功能可用
**下一里程碑**: 权限管理系统实现和考勤管理开发