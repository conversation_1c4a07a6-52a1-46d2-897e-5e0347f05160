-- 更新控制器表，添加缺失的字段

-- 添加控制器编号字段
ALTER TABLE controllers ADD COLUMN controller_number INTEGER;

-- 添加网络模式字段
ALTER TABLE controllers ADD COLUMN network_mode INTEGER NOT NULL DEFAULT 0;

-- 添加手机远程开门字段
ALTER TABLE controllers ADD COLUMN mobile_remote_enabled INTEGER NOT NULL DEFAULT 1;

-- 添加二维码开门字段
ALTER TABLE controllers ADD COLUMN qr_code_enabled INTEGER NOT NULL DEFAULT 1;

-- 创建控制器编号的唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_controllers_controller_number ON controllers(controller_number);

-- 为现有记录设置默认的控制器编号（如果有数据的话）
UPDATE controllers SET controller_number = id WHERE controller_number IS NULL;
