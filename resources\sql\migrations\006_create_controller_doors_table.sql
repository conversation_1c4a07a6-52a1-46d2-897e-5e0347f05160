-- 创建控制器门配置表
-- 用于存储每个控制器的门配置信息

CREATE TABLE IF NOT EXISTS controller_doors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    controller_id INTEGER NOT NULL,             -- 控制器ID
    door_index INTEGER NOT NULL,                -- 门索引（0-3）
    door_name VARCHAR(50) NOT NULL,             -- 门名称
    enabled INTEGER NOT NULL DEFAULT 1,         -- 是否启用
    control_mode INTEGER NOT NULL DEFAULT 0,    -- 控制方式：0-在线，1-常开，2-常闭
    open_delay INTEGER NOT NULL DEFAULT 3,      -- 开门延时(秒)
    
    -- 读卡器配置
    entry_reader_enabled INTEGER NOT NULL DEFAULT 1,   -- 进门读卡器启用
    exit_reader_enabled INTEGER NOT NULL DEFAULT 1,    -- 出门读卡器启用
    entry_attendance INTEGER NOT NULL DEFAULT 0,       -- 进门读卡器作考勤
    exit_attendance INTEGER NOT NULL DEFAULT 0,        -- 出门读卡器作考勤
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (controller_id) REFERENCES controllers(id) ON DELETE CASCADE,
    UNIQUE(controller_id, door_index)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_controller_doors_controller_id ON controller_doors(controller_id);
CREATE INDEX IF NOT EXISTS idx_controller_doors_door_index ON controller_doors(door_index);

-- 创建触发器，在更新controller_doors时自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS trigger_controller_doors_update 
    AFTER UPDATE ON controller_doors
    FOR EACH ROW
BEGIN
    UPDATE controller_doors SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
END;
