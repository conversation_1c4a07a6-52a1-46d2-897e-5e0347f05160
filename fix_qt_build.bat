@echo off
echo Fixing Qt Creator build issue...

REM Set environment variables
set PATH=C:\Qt\Tools\mingw1120_64\bin;C:\Qt\Tools\CMake_64\bin;C:\Qt\Tools\Ninja;%PATH%
set CMAKE_PREFIX_PATH=C:\Qt\6.6.3\mingw_64

REM Remove corrupted build directory
echo Removing corrupted build directory...
if exist "build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug" (
    rmdir /s /q "build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug"
)

REM Create new build directory
echo Creating new build directory...
mkdir "build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug"

REM Enter build directory
cd "build\Desktop_Qt_6_6_3_MinGW_64_bit-Debug"

REM Configure project
echo Configuring CMake project...
cmake ..\.. -G Ninja -DCMAKE_BUILD_TYPE=Debug -DCMAKE_PREFIX_PATH=C:\Qt\6.6.3\mingw_64

REM Check if configuration was successful
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed!
    echo Please check Qt and compiler paths
    pause
    exit /b 1
)

echo CMake configuration successful!
echo You can now rebuild the project in Qt Creator.

REM Try to build the project
echo Attempting to build project...
ninja

REM Check if build was successful
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Executable location: %CD%\AccessControlSystem.exe
) else (
    echo Build had errors, but CMake configuration is fixed.
    echo Please rebuild the project in Qt Creator.
)

pause
