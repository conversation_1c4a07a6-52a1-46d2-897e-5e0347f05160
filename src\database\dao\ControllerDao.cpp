#include "ControllerDao.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlRecord>
#include <QVariant>
#include <QDebug>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

namespace AccessControl {

ControllerDao::ControllerDao(DatabaseProviderPtr provider)
    : m_provider(provider)
{
}

ControllerDao::~ControllerDao() = default;

int ControllerDao::createController(Controller& controller)
{
    if (!m_provider) {
        qWarning() << "ControllerDao: Database provider is null";
        return -1;
    }

    // 验证控制器数据
    auto validation = validateController(controller, false);
    if (!validation.first) {
        qWarning() << "ControllerDao: Validation failed:" << validation.second;
        return -1;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        qWarning() << "ControllerDao: Database is not open";
        return -1;
    }

    QSqlQuery query(db);
    query.prepare(
        "INSERT INTO controllers ("
        "controller_number, serial_number, enabled, ip_address, port, area_id, description, "
        "model, firmware_version, hardware_version, max_doors, max_readers, "
        "online_status, created_at, updated_at"
        ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    );

    bindControllerToQuery(query, controller, false);

    if (query.exec()) {
        int controllerId = m_provider->lastInsertId().toInt();
        controller.setId(controllerId);

        // 保存门配置
        if (!saveDoorConfigs(controllerId, controller.allDoorConfigs())) {
            qWarning() << "ControllerDao: Failed to save door configs";
            // 不回滚，门配置可以后续添加
        }

        qDebug() << "ControllerDao: Controller created with ID:" << controllerId;
        return controllerId;
    }

    qWarning() << "ControllerDao: Failed to create controller:" << query.lastError().text();
    return -1;
}

bool ControllerDao::updateController(const Controller& controller)
{
    if (!m_provider || controller.id() <= 0) {
        return false;
    }

    // 验证控制器数据
    auto validation = validateController(controller, true);
    if (!validation.first) {
        qWarning() << "ControllerDao: Validation failed:" << validation.second;
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);
    query.prepare(
        "UPDATE controllers SET "
        "controller_number = ?, serial_number = ?, enabled = ?, ip_address = ?, port = ?, area_id = ?, description = ?, "
        "model = ?, firmware_version = ?, hardware_version = ?, max_doors = ?, max_readers = ?, "
        "online_status = ?, updated_at = ? "
        "WHERE id = ?"
    );

    bindControllerToQuery(query, controller, true);

    if (query.exec()) {
        // 更新门配置
        deleteDoorConfigs(controller.id());
        saveDoorConfigs(controller.id(), controller.allDoorConfigs());

        qDebug() << "ControllerDao: Controller updated, ID:" << controller.id();
        return true;
    }

    qWarning() << "ControllerDao: Failed to update controller:" << query.lastError().text();
    return false;
}

bool ControllerDao::deleteController(int controllerId)
{
    if (!m_provider || controllerId <= 0) {
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    // 先删除门配置
    deleteDoorConfigs(controllerId);

    QSqlQuery query(db);
    query.prepare("DELETE FROM controllers WHERE id = ?");
    query.addBindValue(controllerId);

    if (query.exec()) {
        qDebug() << "ControllerDao: Controller deleted, ID:" << controllerId;
        return true;
    }

    qWarning() << "ControllerDao: Failed to delete controller:" << query.lastError().text();
    return false;
}

Controller ControllerDao::findById(int controllerId)
{
    Controller controller;

    if (!m_provider || controllerId <= 0) {
        return controller;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controller;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers WHERE id = ?");
    query.addBindValue(controllerId);

    if (query.exec() && query.next()) {
        controller = buildControllerFromQuery(query);
        // 加载门配置
        controller.setAllDoorConfigs(loadDoorConfigs(controllerId));
    }

    return controller;
}

Controller ControllerDao::findBySerialNumber(const QString& serialNumber)
{
    Controller controller;

    if (!m_provider || serialNumber.isEmpty()) {
        return controller;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controller;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers WHERE serial_number = ?");
    query.addBindValue(serialNumber);

    if (query.exec() && query.next()) {
        controller = buildControllerFromQuery(query);
        // 加载门配置
        controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
    }

    return controller;
}

Controller ControllerDao::findByControllerNumber(int controllerNumber)
{
    Controller controller;

    if (!m_provider || controllerNumber <= 0) {
        return controller;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controller;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers WHERE controller_number = ?");
    query.addBindValue(controllerNumber);

    if (query.exec() && query.next()) {
        controller = buildControllerFromQuery(query);
        // 加载门配置
        controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
    }

    return controller;
}

QList<Controller> ControllerDao::findAll()
{
    QList<Controller> controllers;

    if (!m_provider) {
        return controllers;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controllers;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers ORDER BY controller_number");

    if (query.exec()) {
        while (query.next()) {
            Controller controller = buildControllerFromQuery(query);
            // 加载门配置
            controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
            controllers.append(controller);
        }
    }

    return controllers;
}

QList<Controller> ControllerDao::findEnabled()
{
    QList<Controller> controllers;

    if (!m_provider) {
        return controllers;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controllers;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers WHERE enabled = 1 ORDER BY controller_number");

    if (query.exec()) {
        while (query.next()) {
            Controller controller = buildControllerFromQuery(query);
            // 加载门配置
            controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
            controllers.append(controller);
        }
    }

    return controllers;
}

QList<Controller> ControllerDao::findByAreaId(int areaId)
{
    QList<Controller> controllers;

    if (!m_provider) {
        return controllers;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controllers;
    }

    QSqlQuery query(db);
    if (areaId > 0) {
        query.prepare("SELECT * FROM controllers WHERE area_id = ? ORDER BY controller_number");
        query.addBindValue(areaId);
    } else {
        query.prepare("SELECT * FROM controllers WHERE area_id IS NULL ORDER BY controller_number");
    }

    if (query.exec()) {
        while (query.next()) {
            Controller controller = buildControllerFromQuery(query);
            // 加载门配置
            controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
            controllers.append(controller);
        }
    }

    return controllers;
}

QList<Controller> ControllerDao::findByIpAddress(const QString& ipAddress)
{
    QList<Controller> controllers;

    if (!m_provider || ipAddress.isEmpty()) {
        return controllers;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return controllers;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controllers WHERE ip_address = ? ORDER BY controller_number");
    query.addBindValue(ipAddress);

    if (query.exec()) {
        while (query.next()) {
            Controller controller = buildControllerFromQuery(query);
            // 加载门配置
            controller.setAllDoorConfigs(loadDoorConfigs(controller.id()));
            controllers.append(controller);
        }
    }

    return controllers;
}

bool ControllerDao::isSerialNumberExists(const QString& serialNumber, int excludeId)
{
    if (!m_provider || serialNumber.isEmpty()) {
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM controllers WHERE serial_number = ? AND id != ?");
        query.addBindValue(serialNumber);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM controllers WHERE serial_number = ?");
        query.addBindValue(serialNumber);
    }

    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }

    return false;
}

bool ControllerDao::isControllerNumberExists(int controllerNumber, int excludeId)
{
    if (!m_provider || controllerNumber <= 0) {
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);
    if (excludeId > 0) {
        query.prepare("SELECT COUNT(*) FROM controllers WHERE controller_number = ? AND id != ?");
        query.addBindValue(controllerNumber);
        query.addBindValue(excludeId);
    } else {
        query.prepare("SELECT COUNT(*) FROM controllers WHERE controller_number = ?");
        query.addBindValue(controllerNumber);
    }

    if (query.exec() && query.next()) {
        return query.value(0).toInt() > 0;
    }

    return false;
}

int ControllerDao::getNextControllerNumber()
{
    if (!m_provider) {
        return 1;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return 1;
    }

    QSqlQuery query(db);
    query.prepare("SELECT MAX(controller_number) FROM controllers");

    if (query.exec() && query.next()) {
        QVariant maxValue = query.value(0);
        if (maxValue.isNull()) {
            return 1;
        }
        return maxValue.toInt() + 1;
    }

    return 1;
}

QPair<bool, QString> ControllerDao::validateController(const Controller& controller, bool isUpdate)
{
    // 验证控制器编号
    if (!controller.isValidControllerNumber()) {
        return qMakePair(false, "控制器编号必须大于0");
    }

    // 检查控制器编号是否已存在
    if (isControllerNumberExists(controller.controllerNumber(), isUpdate ? controller.id() : -1)) {
        return qMakePair(false, QString("控制器编号 %1 已存在").arg(controller.controllerNumber()));
    }

    // 验证序列号
    if (controller.serialNumber().isEmpty()) {
        return qMakePair(false, "序列号不能为空");
    }

    if (!controller.isValidSerialNumber()) {
        return qMakePair(false, "序列号格式不正确，必须为9位数字且符合规则");
    }

    // 检查序列号是否已存在
    if (isSerialNumberExists(controller.serialNumber(), isUpdate ? controller.id() : -1)) {
        return qMakePair(false, QString("序列号 %1 已存在").arg(controller.serialNumber()));
    }

    // 验证网络配置
    if (controller.networkMode() == Controller::NetworkMode::WAN) {
        if (!controller.isValidIpAddress()) {
            return qMakePair(false, "跨网段模式下IP地址格式不正确");
        }
        if (!controller.isValidPort()) {
            return qMakePair(false, "端口号必须在1-65535范围内");
        }
    }

    // 验证描述长度
    if (!controller.isValidDescription()) {
        return qMakePair(false, "说明文字不能超过60个字符");
    }

    return qMakePair(true, "");
}

QMap<QString, int> ControllerDao::getStatistics()
{
    QMap<QString, int> stats;

    if (!m_provider) {
        return stats;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return stats;
    }

    // 总数
    QSqlQuery query(db);
    query.prepare("SELECT COUNT(*) FROM controllers");
    if (query.exec() && query.next()) {
        stats["total"] = query.value(0).toInt();
    }

    // 启用数
    query.prepare("SELECT COUNT(*) FROM controllers WHERE enabled = 1");
    if (query.exec() && query.next()) {
        stats["enabled"] = query.value(0).toInt();
    }

    // 禁用数
    query.prepare("SELECT COUNT(*) FROM controllers WHERE enabled = 0");
    if (query.exec() && query.next()) {
        stats["disabled"] = query.value(0).toInt();
    }

    return stats;
}

Controller ControllerDao::buildControllerFromQuery(const QSqlQuery& query)
{
    Controller controller;

    controller.setId(query.value("id").toInt());
    controller.setControllerNumber(query.value("controller_number").toInt());
    controller.setSerialNumber(query.value("serial_number").toString());
    controller.setEnabled(query.value("enabled").toBool());

    // 网络配置 - 使用默认值
    controller.setNetworkMode(Controller::NetworkMode::LAN); // 默认局域网
    controller.setIpAddress(query.value("ip_address").toString());
    controller.setPort(query.value("port").toInt());

    // 区域和描述
    controller.setAreaId(query.value("area_id").toInt());
    controller.setDescription(query.value("description").toString());

    // 扩展功能 - 使用默认值
    controller.setMobileRemoteEnabled(true); // 默认启用
    controller.setQrCodeEnabled(true); // 默认启用

    // 时间信息
    controller.setCreatedAt(query.value("created_at").toDateTime());
    controller.setUpdatedAt(query.value("updated_at").toDateTime());

    return controller;
}

void ControllerDao::bindControllerToQuery(QSqlQuery& query, const Controller& controller, bool includeId)
{
    query.addBindValue(controller.controllerNumber());
    query.addBindValue(controller.serialNumber());
    query.addBindValue(controller.enabled());
    query.addBindValue(controller.ipAddress());
    query.addBindValue(controller.port());
    query.addBindValue(controller.areaId() > 0 ? controller.areaId() : QVariant());
    query.addBindValue(controller.description());

    // 添加默认值字段
    query.addBindValue(""); // model - 型号（暂时为空）
    query.addBindValue(""); // firmware_version - 固件版本（暂时为空）
    query.addBindValue(""); // hardware_version - 硬件版本（暂时为空）
    query.addBindValue(4);  // max_doors - 最大门数（默认4）
    query.addBindValue(8);  // max_readers - 最大读卡器数（默认8）
    query.addBindValue(0);  // online_status - 在线状态（默认离线）

    if (!includeId) {
        // INSERT操作需要created_at
        query.addBindValue(QDateTime::currentDateTime()); // created_at
    }
    query.addBindValue(QDateTime::currentDateTime()); // updated_at

    if (includeId) {
        query.addBindValue(controller.id());
    }
}

bool ControllerDao::saveDoorConfigs(int controllerId, const QList<Controller::DoorConfig>& doorConfigs)
{
    if (!m_provider || controllerId <= 0) {
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    // 先删除现有的门配置
    deleteDoorConfigs(controllerId);

    // 插入新的门配置
    bool allSuccess = true;
    for (int i = 0; i < doorConfigs.size(); ++i) {
        const Controller::DoorConfig& config = doorConfigs[i];

        // 为每个门配置创建新的查询对象，避免重复使用导致的绑定问题
        QSqlQuery query(db);
        query.prepare(
            "INSERT INTO controller_doors ("
            "controller_id, door_index, door_name, enabled, control_mode, open_delay, door_icon, "
            "entry_reader_enabled, exit_reader_enabled, entry_reader_type, exit_reader_type, "
            "entry_attendance, exit_attendance"
            ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        );

        query.addBindValue(controllerId);
        query.addBindValue(i);
        query.addBindValue(config.name);
        query.addBindValue(config.enabled);
        query.addBindValue(static_cast<int>(config.controlMode));
        query.addBindValue(config.openDelay);
        query.addBindValue(static_cast<int>(config.doorIcon));
        query.addBindValue(config.entryReaderEnabled);
        query.addBindValue(config.exitReaderEnabled);
        query.addBindValue(config.entryReaderType);
        query.addBindValue(config.exitReaderType);
        query.addBindValue(config.entryAttendance);
        query.addBindValue(config.exitAttendance);

        if (!query.exec()) {
            qWarning() << "ControllerDao: Failed to save door config" << i << ":" << query.lastError().text();
            qWarning() << "ControllerDao: SQL:" << query.lastQuery();
            qWarning() << "ControllerDao: Bound values:" << query.boundValues();
            allSuccess = false;
        } else {
            qDebug() << "ControllerDao: Successfully saved door config" << i << "for controller" << controllerId;
        }
    }

    return allSuccess;
}

QList<Controller::DoorConfig> ControllerDao::loadDoorConfigs(int controllerId)
{
    QList<Controller::DoorConfig> doorConfigs;

    if (!m_provider || controllerId <= 0) {
        return doorConfigs;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return doorConfigs;
    }

    QSqlQuery query(db);
    query.prepare("SELECT * FROM controller_doors WHERE controller_id = ? ORDER BY door_index");
    query.addBindValue(controllerId);

    if (query.exec()) {
        while (query.next()) {
            Controller::DoorConfig config;
            config.name = query.value("door_name").toString();
            config.enabled = query.value("enabled").toBool();
            config.controlMode = static_cast<Controller::DoorControlMode>(query.value("control_mode").toInt());
            config.openDelay = query.value("open_delay").toInt();
            config.doorIcon = static_cast<Controller::DoorIcon>(query.value("door_icon").toInt());
            config.entryReaderEnabled = query.value("entry_reader_enabled").toBool();
            config.exitReaderEnabled = query.value("exit_reader_enabled").toBool();
            config.entryReaderType = query.value("entry_reader_type").toString();
            config.exitReaderType = query.value("exit_reader_type").toString();
            config.entryAttendance = query.value("entry_attendance").toBool();
            config.exitAttendance = query.value("exit_attendance").toBool();

            doorConfigs.append(config);
        }
    }

    // 如果没有门配置，创建默认配置
    if (doorConfigs.isEmpty()) {
        for (int i = 0; i < 4; ++i) {
            Controller::DoorConfig config;
            config.name = QString("门%1").arg(i + 1);
            config.enabled = (i == 0); // 默认只启用第一个门
            doorConfigs.append(config);
        }
    }

    return doorConfigs;
}

bool ControllerDao::deleteDoorConfigs(int controllerId)
{
    if (!m_provider || controllerId <= 0) {
        return false;
    }

    QSqlDatabase db = m_provider->getDatabase();
    if (!db.isOpen()) {
        return false;
    }

    QSqlQuery query(db);
    query.prepare("DELETE FROM controller_doors WHERE controller_id = ?");
    query.addBindValue(controllerId);

    if (query.exec()) {
        return true;
    }

    qWarning() << "ControllerDao: Failed to delete door configs:" << query.lastError().text();
    return false;
}

} // namespace AccessControl