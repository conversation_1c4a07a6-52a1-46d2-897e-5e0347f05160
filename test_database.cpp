#include <QApplication>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置数据库路径
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QString dbPath = dataDir + "/access_control.db";
    
    qDebug() << "数据库路径:" << dbPath;
    
    // 检查数据库文件是否存在
    if (!QFile::exists(dbPath)) {
        qDebug() << "数据库文件不存在";
        return 1;
    }
    
    // 连接数据库
    QSqlDatabase db = QSqlDatabase::addDatabase("QSQLITE");
    db.setDatabaseName(dbPath);
    
    if (!db.open()) {
        qDebug() << "无法打开数据库:" << db.lastError().text();
        return 1;
    }
    
    qDebug() << "数据库连接成功";
    
    // 检查areas表是否存在
    QSqlQuery query(db);
    if (query.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='areas'")) {
        if (query.next()) {
            qDebug() << "areas表存在";
            
            // 获取表结构
            if (query.exec("PRAGMA table_info(areas)")) {
                qDebug() << "areas表结构:";
                while (query.next()) {
                    QString name = query.value(1).toString();
                    QString type = query.value(2).toString();
                    bool notNull = query.value(3).toBool();
                    QString defaultValue = query.value(4).toString();
                    qDebug() << "  " << name << type << (notNull ? "NOT NULL" : "") << (defaultValue.isEmpty() ? "" : "DEFAULT " + defaultValue);
                }
            }
        } else {
            qDebug() << "areas表不存在";
        }
    } else {
        qDebug() << "查询失败:" << query.lastError().text();
    }
    
    db.close();
    return 0;
}
